# 🚀 Text2SQL元数据集成部署检查清单

## 📋 部署前检查

### ✅ 1. 代码修改确认

**已完成的文件修改**:
- [x] `chatdb/backend/app/core/config.py` - 添加多数据库配置
- [x] `chatdb/backend/app/services/text2sql_utils.py` - 添加元数据获取函数
- [x] `chatdb/backend/app/services/text2sql_service.py` - 集成元数据增强

**语法检查**:
```bash
cd chatdb/backend
python -m py_compile app/core/config.py
python -m py_compile app/services/text2sql_utils.py
python -m py_compile app/services/text2sql_service.py
```

### ✅ 2. 配置文件确认

**环境变量配置** (`.env`):
```env
# 主数据库配置
SQLITE_DB_PATH=C:\Users\<USER>\PycharmProjects\智能数据分析系统\resource.db

# 多数据库配置
METADATA_DB_PATH=C:\Users\<USER>\PycharmProjects\智能数据分析系统\resource.db
BUSINESS_DB_PATH=C:\Users\<USER>\PycharmProjects\智能数据分析系统\fin_data.db

# 元数据增强开关
ENABLE_METADATA_ENHANCEMENT=true
```

### ✅ 3. 数据库状态确认

**数据库文件检查**:
- [x] `resource.db` 存在且包含元数据表
- [x] `fin_data.db` 存在且包含业务数据
- [x] 无重复表，架构完全合规

**元数据表验证**:
```sql
-- 检查resource.db中的元数据表
SELECT COUNT(*) FROM table_descriptions;     -- 应该返回 1
SELECT COUNT(*) FROM column_descriptions;    -- 应该返回 31
SELECT COUNT(*) FROM business_rules;         -- 应该返回 5
```

## 🔧 部署步骤

### 步骤1: 停止现有服务
```bash
# 查找并停止现有的uvicorn进程
ps aux | grep uvicorn
pkill -f "uvicorn app.main:app"

# 或者如果在Docker中运行
docker stop chatdb-backend
```

### 步骤2: 验证代码修改
```bash
cd chatdb/backend

# 检查Python语法
python -c "
import app.core.config
import app.services.text2sql_utils
import app.services.text2sql_service
print('✅ 所有模块导入成功')
"

# 测试元数据加载
python -c "
from app.services.text2sql_utils import get_financial_metadata
metadata = get_financial_metadata()
print(f'元数据状态: {metadata.get(\"has_metadata\", False)}')
print(f'字段数量: {len(metadata.get(\"column_descriptions\", []))}')
print(f'规则数量: {len(metadata.get(\"business_rules\", []))}')
"
```

### 步骤3: 启动增强版服务
```bash
cd chatdb/backend

# 启动服务（开发模式）
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000

# 或者启动服务（生产模式）
python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --workers 4
```

### 步骤4: 健康检查
```bash
# 检查服务状态
curl http://localhost:8000/health

# 检查API文档
curl http://localhost:8000/docs
```

## 🧪 功能验证

### 验证1: 运行集成测试
```bash
python test_metadata_integration.py
```

**预期结果**:
- ✅ 元数据加载: 31个字段, 5个规则
- ✅ API集成: 所有测试用例通过
- ✅ 配置检查: 所有配置项正确

### 验证2: 手动API测试

**测试收入查询**:
```bash
curl -X POST "http://localhost:8000/v1/query/" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "查询2024年9月的收入情况",
    "connection_id": 1
  }'
```

**预期响应**:
```json
{
  "sql": "SELECT ... SUM(credit_amount) ... WHERE account_code LIKE '60%' ...",
  "results": [...],
  "error": null,
  "context": {
    "metadata_enhanced": true,
    "business_rules_applied": 5
  }
}
```

**测试资产查询**:
```bash
curl -X POST "http://localhost:8000/v1/query/" \
  -H "Content-Type: application/json" \
  -d '{
    "query": "显示2024年9月的资产余额",
    "connection_id": 1
  }'
```

**预期响应**:
```json
{
  "sql": "SELECT ... SUM(CAST(balance AS REAL)) ... WHERE account_code LIKE '1%' ...",
  "results": [...],
  "error": null,
  "context": {
    "metadata_enhanced": true,
    "business_rules_applied": 5
  }
}
```

### 验证3: 关键功能点检查

**必须验证的功能**:
- [x] 收入查询使用 `credit_amount` 字段
- [x] 资产查询使用 `balance` 字段 + `CAST(balance AS REAL)`
- [x] 费用查询使用 `debit_amount` 字段
- [x] 科目编号规律正确应用 (1xxx, 60xx, 64xx等)
- [x] 元数据增强标志 `metadata_enhanced: true`
- [x] 业务规则应用数量 `business_rules_applied: 5`

## 🚨 故障排除

### 常见问题与解决方案

**问题1: 服务启动失败**
```
解决方案:
1. 检查Python路径和依赖
2. 检查配置文件语法
3. 查看详细错误日志
```

**问题2: 元数据加载失败**
```
解决方案:
1. 检查resource.db文件是否存在
2. 验证元数据表是否存在
3. 检查数据库文件权限
```

**问题3: API返回metadata_enhanced: false**
```
解决方案:
1. 检查ENABLE_METADATA_ENHANCEMENT配置
2. 验证financial_data表是否被识别
3. 检查元数据获取函数日志
```

**问题4: SQL生成不正确**
```
解决方案:
1. 检查业务规则是否正确加载
2. 验证prompt构建是否包含元数据
3. 查看LLM响应和SQL提取过程
```

### 调试命令

**查看服务日志**:
```bash
# 如果使用systemd
journalctl -u chatdb-backend -f

# 如果直接运行
tail -f /var/log/chatdb/backend.log
```

**测试元数据加载**:
```python
python -c "
import sys
sys.path.append('chatdb/backend')
from app.services.text2sql_utils import get_financial_metadata
import json
metadata = get_financial_metadata()
print(json.dumps(metadata, indent=2, ensure_ascii=False))
"
```

**测试数据库连接**:
```python
python -c "
import sqlite3
conn = sqlite3.connect('resource.db')
cursor = conn.cursor()
cursor.execute('SELECT COUNT(*) FROM business_rules')
print(f'业务规则数量: {cursor.fetchone()[0]}')
conn.close()
"
```

## 📊 性能监控

### 关键指标

**响应时间**:
- 元数据加载: < 100ms
- SQL生成: < 2s
- 总查询时间: < 5s

**成功率**:
- 元数据加载成功率: > 99%
- SQL生成成功率: > 95%
- 查询执行成功率: > 90%

**资源使用**:
- 内存使用: < 512MB
- CPU使用: < 50%
- 磁盘I/O: 正常

### 监控命令

**检查进程状态**:
```bash
ps aux | grep uvicorn
top -p $(pgrep -f uvicorn)
```

**检查端口占用**:
```bash
netstat -tlnp | grep :8000
lsof -i :8000
```

**检查日志**:
```bash
tail -f chatdb/backend/logs/app.log
grep "元数据" chatdb/backend/logs/app.log
```

## ✅ 部署成功标准

### 技术指标
- [x] 服务正常启动，端口8000可访问
- [x] 健康检查接口返回200
- [x] 元数据加载成功 (31字段+5规则)
- [x] 所有测试用例通过

### 功能指标
- [x] 收入查询生成正确SQL (使用credit_amount)
- [x] 资产查询生成正确SQL (使用balance+类型转换)
- [x] 费用查询生成正确SQL (使用debit_amount)
- [x] API响应包含metadata_enhanced: true

### 业务指标
- [x] 财务查询结果准确
- [x] 业务规则正确应用
- [x] 查询响应时间合理
- [x] 系统稳定运行

## 🎉 部署完成

当所有检查项都通过时，元数据集成部署成功！

**下一步**:
1. 通知相关团队部署完成
2. 更新文档和用户指南
3. 开始收集用户反馈
4. 监控系统运行状态

**长期维护**:
1. 定期检查元数据完整性
2. 监控查询质量和准确性
3. 根据用户反馈优化业务规则
4. 扩展元数据系统到其他表
