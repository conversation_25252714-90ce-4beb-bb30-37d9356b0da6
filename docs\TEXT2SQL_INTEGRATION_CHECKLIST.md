# Text2SQL元数据系统集成详细清单

## 📋 集成概述

基于已完成的数据库架构优化（fin_data.db包含业务数据，resource.db包含元数据），现在需要修改Text2SQL服务以支持多数据库元数据增强查询。

## 🔧 1. 代码集成任务

### 1.1 核心文件修改清单

**需要修改的文件**:
1. `chatdb/backend/app/core/config.py` - 添加多数据库配置
2. `chatdb/backend/app/services/text2sql_service.py` - 集成元数据增强
3. `chatdb/backend/app/services/text2sql_utils.py` - 添加元数据获取函数
4. `chatdb/backend/app/db/session.py` - 支持多数据库连接（可选）

### 1.2 详细修改内容

#### 文件1: `chatdb/backend/app/core/config.py`

**添加多数据库配置支持**:
```python
class Settings(BaseSettings):
    # 现有配置...
    
    # 多数据库配置
    METADATA_DB_PATH: str = os.getenv(
        "METADATA_DB_PATH", 
        "C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\resource.db"
    )
    BUSINESS_DB_PATH: str = os.getenv(
        "BUSINESS_DB_PATH", 
        "C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\fin_data.db"
    )
    
    # 元数据增强开关
    ENABLE_METADATA_ENHANCEMENT: bool = os.getenv("ENABLE_METADATA_ENHANCEMENT", "true").lower() == "true"
```

#### 文件2: `chatdb/backend/app/services/text2sql_utils.py`

**添加元数据获取函数**:
```python
import sqlite3
from typing import Dict, Any, Optional
from app.core.config import settings

def get_financial_metadata(table_name: str = "financial_data") -> Dict[str, Any]:
    """从元数据库获取财务表的元数据信息"""
    
    if not settings.ENABLE_METADATA_ENHANCEMENT:
        return {"has_metadata": False}
    
    try:
        conn = sqlite3.connect(settings.METADATA_DB_PATH)
        cursor = conn.cursor()
        
        metadata = {
            "table_description": None,
            "column_descriptions": [],
            "business_rules": [],
            "has_metadata": False
        }
        
        # 检查元数据表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='table_descriptions'")
        if not cursor.fetchone():
            conn.close()
            return metadata
        
        # 查询表描述
        cursor.execute("""
            SELECT description, business_purpose, data_scale 
            FROM table_descriptions 
            WHERE table_name = ?
        """, (table_name,))
        
        table_desc = cursor.fetchone()
        if table_desc:
            metadata["table_description"] = {
                "description": table_desc[0],
                "business_purpose": table_desc[1],
                "data_scale": table_desc[2]
            }
        
        # 查询字段描述
        cursor.execute("""
            SELECT column_name, chinese_name, description, data_type, 
                   business_rules, ai_understanding_points
            FROM column_descriptions 
            WHERE table_name = ?
            ORDER BY column_name
        """, (table_name,))
        
        for col in cursor.fetchall():
            metadata["column_descriptions"].append({
                "column_name": col[0],
                "chinese_name": col[1],
                "description": col[2],
                "data_type": col[3],
                "business_rules": col[4],
                "ai_understanding_points": col[5]
            })
        
        # 查询业务规则
        cursor.execute("""
            SELECT rule_category, rule_description, sql_example, importance_level
            FROM business_rules 
            WHERE table_name = ?
            ORDER BY 
                CASE importance_level 
                    WHEN 'CRITICAL' THEN 1 
                    WHEN 'HIGH' THEN 2 
                    WHEN 'MEDIUM' THEN 3 
                    ELSE 4 
                END
        """, (table_name,))
        
        for rule in cursor.fetchall():
            metadata["business_rules"].append({
                "category": rule[0],
                "description": rule[1],
                "sql_example": rule[2],
                "importance": rule[3]
            })
        
        metadata["has_metadata"] = True
        conn.close()
        
        logger.info(f"成功加载元数据: {len(metadata['column_descriptions'])}个字段, {len(metadata['business_rules'])}个规则")
        return metadata
        
    except Exception as e:
        logger.error(f"获取元数据失败: {e}")
        return {"has_metadata": False}

def enhance_schema_with_metadata(schema_context: Dict[str, Any], metadata: Dict[str, Any]) -> Dict[str, Any]:
    """使用元数据增强schema上下文"""
    
    if not metadata.get("has_metadata"):
        return schema_context
    
    # 增强表信息
    for table in schema_context.get("tables", []):
        if table.get("name") == "financial_data":
            # 添加表描述
            if metadata.get("table_description"):
                table["enhanced_description"] = metadata["table_description"]["description"]
                table["business_purpose"] = metadata["table_description"]["business_purpose"]
            
            # 增强列信息
            column_meta_map = {col["column_name"]: col for col in metadata.get("column_descriptions", [])}
            
            for column in table.get("columns", []):
                col_name = column.get("column_name")
                if col_name in column_meta_map:
                    meta_col = column_meta_map[col_name]
                    column["chinese_name"] = meta_col["chinese_name"]
                    column["enhanced_description"] = meta_col["ai_understanding_points"]
                    column["business_rules"] = meta_col["business_rules"]
    
    return schema_context
```

#### 文件3: `chatdb/backend/app/services/text2sql_service.py`

**修改主要函数**:

**1. 修改 `construct_prompt` 函数**:
```python
def construct_prompt(schema_context: Dict[str, Any], natural_language_query: str, 
                    value_mappings: Dict[str, Dict[str, str]], 
                    metadata: Optional[Dict[str, Any]] = None) -> str:
    """构建包含元数据的增强prompt"""
    
    # 原有的schema和值映射处理
    schema_str = format_schema_for_prompt(schema_context)
    mappings_str = format_value_mappings_for_prompt(value_mappings)
    
    # 构建元数据增强部分
    metadata_enhancement = ""
    if metadata and metadata.get("has_metadata"):
        metadata_enhancement = build_metadata_enhancement_prompt(metadata)
    
    # 构建完整prompt
    prompt = f"""
你是一名专业的SQL开发专家，专门将自然语言问题转换为精确的SQL查询。

### 数据库结构:
{schema_str}

{mappings_str}

{metadata_enhancement}

### 自然语言问题:
{natural_language_query}

### 指令:
请根据上述数据库结构和业务规则，生成准确的SQL查询语句。
特别注意：
1. 严格遵循提供的业务规则
2. 使用正确的字段进行汇总和计算
3. 注意数据类型转换要求
4. 确保SQL语法正确且高效

请生成SQL查询:
"""
    
    return prompt

def build_metadata_enhancement_prompt(metadata: Dict[str, Any]) -> str:
    """构建元数据增强的prompt部分"""
    
    enhancement = "\n### 🎯 财务业务元数据 (重要!):\n\n"
    
    # 表描述
    if metadata.get("table_description"):
        table_desc = metadata["table_description"]
        enhancement += f"**表说明**: {table_desc['description']}\n"
        enhancement += f"**业务用途**: {table_desc['business_purpose']}\n\n"
    
    # 关键业务规则
    if metadata.get("business_rules"):
        enhancement += "### ⚠️ 关键业务规则 (必须遵守!):\n\n"
        for rule in metadata["business_rules"]:
            if rule["importance"] in ["CRITICAL", "HIGH"]:
                enhancement += f"**【{rule['importance']}】{rule['category']}**:\n"
                enhancement += f"- {rule['description']}\n"
                if rule["sql_example"]:
                    enhancement += f"- 示例: `{rule['sql_example']}`\n"
                enhancement += "\n"
    
    # 关键字段说明
    if metadata.get("column_descriptions"):
        enhancement += "### 📋 关键字段说明:\n\n"
        
        # 重点显示金额字段
        amount_fields = []
        for col in metadata["column_descriptions"]:
            col_name = col["column_name"]
            if "amount" in col_name or col_name == "balance":
                chinese_name = col["chinese_name"]
                ai_points = col["ai_understanding_points"]
                amount_fields.append(f"- **{col_name}** ({chinese_name}): {ai_points}")
        
        if amount_fields:
            enhancement += "**💰 金额字段 (关键)**:\n"
            for field in amount_fields:
                enhancement += f"{field}\n"
            enhancement += "\n"
    
    return enhancement
```

**2. 修改 `process_text2sql_query` 函数**:
```python
def process_text2sql_query(db: Session, connection: DBConnection, natural_language_query: str) -> QueryResponse:
    """处理text2sql查询请求 - 增强版"""
    
    try:
        logger.info(f"处理Text2SQL查询: {natural_language_query}")
        
        # 1. 检索相关schema (保持原有逻辑)
        schema_context = retrieve_relevant_schema(db, connection.id, natural_language_query)
        
        if not schema_context["tables"]:
            return QueryResponse(
                sql="",
                results=None,
                error="无法为此查询识别相关表。",
                context={"schema_context": schema_context}
            )
        
        # 2. 获取值映射 (保持原有逻辑)
        value_mappings = get_value_mappings(db, schema_context)
        
        # 3. 🆕 获取财务元数据
        metadata = None
        if any(table.get("name") == "financial_data" for table in schema_context["tables"]):
            metadata = get_financial_metadata("financial_data")
            if metadata.get("has_metadata"):
                # 使用元数据增强schema上下文
                schema_context = enhance_schema_with_metadata(schema_context, metadata)
        
        # 4. 🆕 构建增强prompt
        prompt = construct_prompt(schema_context, natural_language_query, value_mappings, metadata)
        
        # 5. 调用LLM API (保持原有逻辑)
        llm_response = call_llm_api(prompt)
        
        # 6. 提取和处理SQL (保持原有逻辑)
        sql = extract_sql_from_llm_response(llm_response)
        processed_sql = process_sql_with_value_mappings(sql, value_mappings)
        
        # 7. 验证SQL (保持原有逻辑)
        if not validate_sql(processed_sql):
            return QueryResponse(
                sql=processed_sql,
                results=None,
                error="生成的SQL验证失败。",
                context={
                    "schema_context": schema_context,
                    "metadata_enhanced": metadata is not None,
                    "prompt": prompt,
                    "llm_response": llm_response
                }
            )
        
        # 8. 执行SQL查询 (保持原有逻辑)
        engine = get_db_engine(connection)
        with engine.connect() as conn:
            result = conn.execute(processed_sql)
            rows = result.fetchall()
            columns = list(result.keys())
        
        # 格式化结果
        results = []
        for row in rows:
            results.append(dict(zip(columns, row)))
        
        logger.info(f"查询成功，返回 {len(results)} 条记录")
        
        return QueryResponse(
            sql=processed_sql,
            results=results,
            error=None,
            context={
                "schema_context": schema_context,
                "metadata_enhanced": metadata.get("has_metadata", False) if metadata else False,
                "business_rules_applied": len(metadata.get("business_rules", [])) if metadata else 0
            }
        )
        
    except Exception as e:
        logger.error(f"Text2SQL查询处理失败: {e}")
        return QueryResponse(
            sql="",
            results=None,
            error=f"查询处理失败: {str(e)}",
            context=None
        )
```

## ⚙️ 2. 配置调整

### 2.1 环境变量配置

**当前 `.env` 文件已正确配置**:
```env
# 主数据库配置（系统表+元数据）
SQLITE_DB_PATH=C:\Users\<USER>\PycharmProjects\智能数据分析系统\resource.db

# 多数据库配置
METADATA_DB_PATH=C:\Users\<USER>\PycharmProjects\智能数据分析系统\resource.db
BUSINESS_DB_PATH=C:\Users\<USER>\PycharmProjects\智能数据分析系统\fin_data.db

# 元数据增强开关
ENABLE_METADATA_ENHANCEMENT=true
```

### 2.2 数据库连接配置

**无需修改** - 当前配置已正确：
- 主系统连接：resource.db（包含系统表和元数据）
- 业务查询：通过现有连接机制访问fin_data.db

### 2.3 依赖包检查

**无需新增依赖** - 使用标准库：
- `sqlite3` - 元数据查询
- `logging` - 日志记录
- 现有的SQLAlchemy和FastAPI依赖

## 🧪 3. 功能验证

### 3.1 验证脚本

创建 `test_metadata_integration.py`:
```python
#!/usr/bin/env python3
import requests
import json

def test_metadata_enhanced_queries():
    """测试元数据增强的查询功能"""
    
    base_url = "http://localhost:8000/v1"
    
    test_cases = [
        {
            "name": "收入查询测试",
            "query": "查询2024年9月的收入情况",
            "expected_field": "credit_amount",
            "expected_pattern": "60%"
        },
        {
            "name": "资产查询测试", 
            "query": "显示2024年9月的资产余额",
            "expected_field": "balance",
            "expected_cast": "CAST(balance AS REAL)"
        },
        {
            "name": "费用查询测试",
            "query": "分析2024年9月的管理费用",
            "expected_field": "debit_amount",
            "expected_pattern": "64%"
        }
    ]
    
    for test in test_cases:
        print(f"\n🧪 {test['name']}")
        
        response = requests.post(
            f"{base_url}/query/",
            json={
                "query": test["query"],
                "connection_id": 1
            }
        )
        
        if response.status_code == 200:
            result = response.json()
            sql = result.get("sql", "")
            
            # 验证是否使用了正确的字段
            if test["expected_field"] in sql:
                print(f"✅ 正确使用了 {test['expected_field']} 字段")
            else:
                print(f"❌ 未使用预期的 {test['expected_field']} 字段")
            
            # 验证业务规则应用
            if result.get("context", {}).get("metadata_enhanced"):
                print(f"✅ 元数据增强已应用")
            else:
                print(f"❌ 元数据增强未应用")
            
            print(f"📝 生成的SQL: {sql}")
        else:
            print(f"❌ 请求失败: {response.status_code}")

if __name__ == "__main__":
    test_metadata_enhanced_queries()
```

### 3.2 关键验证点

**必须验证的功能**:
1. ✅ 元数据正确加载（31个字段描述 + 5个业务规则）
2. ✅ 收入查询使用 `credit_amount` 字段
3. ✅ 资产查询使用 `balance` 字段 + `CAST(balance AS REAL)`
4. ✅ 费用查询使用 `debit_amount` 字段
5. ✅ 科目编号规律正确应用（1xxx, 60xx, 64xx等）

## 🚀 4. 部署准备

### 4.1 部署步骤

**步骤1**: 应用代码修改
```bash
# 1. 修改配置文件
# 2. 修改Python源码文件
# 3. 验证语法正确性
python -m py_compile chatdb/backend/app/services/text2sql_service.py
python -m py_compile chatdb/backend/app/services/text2sql_utils.py
```

**步骤2**: 重启服务
```bash
cd chatdb/backend
# 停止现有服务
pkill -f "uvicorn app.main:app"

# 启动增强版服务
python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

**步骤3**: 功能验证
```bash
# 运行集成测试
python test_metadata_integration.py

# 手动API测试
curl -X POST "http://localhost:8000/v1/query/" \
  -H "Content-Type: application/json" \
  -d '{"query": "查询2024年9月的收入情况", "connection_id": 1}'
```

### 4.2 兼容性检查

**潜在问题与解决方案**:

1. **导入路径问题**:
   ```python
   # 确保正确导入
   from app.core.config import settings
   from app.services.text2sql_utils import get_financial_metadata
   ```

2. **数据库路径问题**:
   ```python
   # 使用绝对路径，避免相对路径问题
   METADATA_DB_PATH = os.path.abspath(settings.METADATA_DB_PATH)
   ```

3. **日志配置**:
   ```python
   import logging
   logger = logging.getLogger(__name__)
   ```

### 4.3 监控和调试

**添加调试日志**:
```python
# 在关键位置添加日志
logger.info(f"元数据增强状态: {metadata.get('has_metadata', False)}")
logger.info(f"应用的业务规则数量: {len(metadata.get('business_rules', []))}")
logger.debug(f"增强后的prompt长度: {len(prompt)}")
```

## 📋 5. 实施时间表

### 阶段1: 代码修改 (30分钟)
- [ ] 修改 `config.py` 添加多数据库配置
- [ ] 修改 `text2sql_utils.py` 添加元数据函数
- [ ] 修改 `text2sql_service.py` 集成元数据增强

### 阶段2: 测试验证 (20分钟)
- [ ] 语法检查和导入验证
- [ ] 创建并运行测试脚本
- [ ] 验证关键功能点

### 阶段3: 部署上线 (10分钟)
- [ ] 重启服务
- [ ] 端到端功能测试
- [ ] 监控日志确认正常运行

**总预计时间**: 60分钟

## 🎯 成功标准

集成成功的标志：
1. ✅ 服务正常启动，无错误日志
2. ✅ 元数据正确加载（31字段+5规则）
3. ✅ 财务查询生成正确SQL（使用正确金额字段）
4. ✅ API响应包含 `metadata_enhanced: true`
5. ✅ 查询结果准确，符合财务业务逻辑

完成这些修改后，您的智能数据分析系统将具备真正的财务专业AI能力！
