#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
元数据专项迁移脚本
仅将元数据表从fin_data.db迁移到resource.db，保持业务数据分离
"""

import sqlite3
import os
import shutil
from datetime import datetime

def backup_databases():
    """备份数据库文件"""
    print("🔒 备份数据库文件...")
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    # 备份resource.db
    if os.path.exists("resource.db"):
        backup_path = f"resource.db.backup_{timestamp}"
        shutil.copy2("resource.db", backup_path)
        print(f"✅ resource.db备份到: {backup_path}")
    
    # 备份fin_data.db
    if os.path.exists("fin_data.db"):
        backup_path = f"fin_data.db.backup_{timestamp}"
        shutil.copy2("fin_data.db", backup_path)
        print(f"✅ fin_data.db备份到: {backup_path}")

def migrate_metadata_tables():
    """迁移元数据表到resource.db"""
    print("\n📦 开始迁移元数据表...")
    
    source_db = "fin_data.db"
    target_db = "resource.db"
    
    if not os.path.exists(source_db):
        print(f"❌ 源数据库不存在: {source_db}")
        return False
    
    if not os.path.exists(target_db):
        print(f"❌ 目标数据库不存在: {target_db}")
        return False
    
    try:
        source_conn = sqlite3.connect(source_db)
        target_conn = sqlite3.connect(target_db)
        
        # 要迁移的元数据表
        metadata_tables = [
            'table_descriptions',
            'column_descriptions', 
            'business_rules'
        ]
        
        for table in metadata_tables:
            print(f"\n📋 迁移表: {table}")
            
            # 检查源表是否存在
            source_cursor = source_conn.cursor()
            source_cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'")
            if not source_cursor.fetchone():
                print(f"   ⚠️  源表不存在，跳过")
                continue
            
            # 获取表结构
            source_cursor.execute(f"SELECT sql FROM sqlite_master WHERE type='table' AND name='{table}'")
            create_sql = source_cursor.fetchone()[0]
            
            # 在目标数据库创建表
            target_cursor = target_conn.cursor()
            target_cursor.execute(f"DROP TABLE IF EXISTS {table}")
            target_cursor.execute(create_sql)
            print(f"   ✅ 创建表结构")
            
            # 迁移数据
            source_cursor.execute(f"SELECT * FROM {table}")
            rows = source_cursor.fetchall()
            
            if rows:
                column_count = len(rows[0])
                placeholders = ','.join(['?' for _ in range(column_count)])
                target_cursor.executemany(f"INSERT INTO {table} VALUES ({placeholders})", rows)
                print(f"   ✅ 迁移了 {len(rows)} 条记录")
            else:
                print(f"   ⚠️  表为空")
        
        # 迁移视图（需要修改以支持跨库查询）
        print(f"\n🔍 处理视图...")
        views_to_migrate = ['financial_data_with_metadata', 'financial_data_columns_metadata']
        
        for view in views_to_migrate:
            source_cursor.execute(f"SELECT sql FROM sqlite_master WHERE type='view' AND name='{view}'")
            view_sql = source_cursor.fetchone()
            if view_sql:
                # 创建修改后的视图（暂时不包含跨库引用）
                target_cursor.execute(f"DROP VIEW IF EXISTS {view}")
                print(f"   ✅ 准备视图: {view} (需要后续配置跨库查询)")
        
        target_conn.commit()
        source_conn.close()
        target_conn.close()
        
        print(f"\n🎉 元数据迁移完成！")
        return True
        
    except Exception as e:
        print(f"❌ 迁移失败: {e}")
        return False

def verify_migration():
    """验证迁移结果"""
    print(f"\n🧪 验证迁移结果...")
    
    try:
        # 验证resource.db中的元数据
        conn = sqlite3.connect("resource.db")
        cursor = conn.cursor()
        
        metadata_tables = ['table_descriptions', 'column_descriptions', 'business_rules']
        
        print(f"📊 resource.db中的元数据:")
        for table in metadata_tables:
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            count = cursor.fetchone()[0]
            print(f"   ✅ {table}: {count} 条记录")
        
        # 验证系统表
        cursor.execute("SELECT COUNT(*) FROM dbconnection")
        db_conn_count = cursor.fetchone()[0]
        print(f"   ✅ dbconnection: {db_conn_count} 条记录")
        
        conn.close()
        
        # 验证fin_data.db中的业务数据
        conn = sqlite3.connect("fin_data.db")
        cursor = conn.cursor()
        
        cursor.execute("SELECT COUNT(*) FROM financial_data")
        business_count = cursor.fetchone()[0]
        print(f"\n💼 fin_data.db中的业务数据:")
        print(f"   ✅ financial_data: {business_count:,} 条记录")
        
        conn.close()
        
        print(f"\n✅ 验证完成：数据分离成功！")
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def update_env_config():
    """更新环境配置文件"""
    print(f"\n⚙️ 更新配置文件...")
    
    env_file = "chatdb/backend/.env"
    
    if not os.path.exists(env_file):
        print(f"❌ 配置文件不存在: {env_file}")
        return False
    
    try:
        # 备份配置文件
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_path = f"{env_file}.backup_{timestamp}"
        shutil.copy2(env_file, backup_path)
        print(f"✅ 配置文件备份到: {backup_path}")
        
        # 读取配置文件
        with open(env_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 修改配置
        modified = False
        new_lines = []
        
        for line in lines:
            if line.startswith('SQLITE_DB_PATH='):
                # 修改主数据库路径指向resource.db
                new_line = 'SQLITE_DB_PATH=C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\resource.db\n'
                new_lines.append(new_line)
                print(f"📝 修改: SQLITE_DB_PATH -> resource.db")
                modified = True
            else:
                new_lines.append(line)
        
        # 添加新的配置项
        if modified:
            new_lines.append('\n# 多数据库配置\n')
            new_lines.append('METADATA_DB_PATH=C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\resource.db\n')
            new_lines.append('BUSINESS_DB_PATH=C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\fin_data.db\n')
            print(f"📝 添加: 多数据库配置项")
        
        # 写回配置文件
        with open(env_file, 'w', encoding='utf-8') as f:
            f.writelines(new_lines)
        
        print(f"✅ 配置文件更新完成")
        return True
        
    except Exception as e:
        print(f"❌ 配置更新失败: {e}")
        return False

def show_next_steps():
    """显示后续步骤"""
    print(f"\n🚀 后续步骤:")
    print(f"=" * 60)
    print(f"1. 修改 text2sql_service.py 支持多数据库查询")
    print(f"2. 更新元数据获取函数连接 resource.db")
    print(f"3. 更新业务查询函数连接 fin_data.db")
    print(f"4. 测试跨库的元数据增强查询功能")
    print(f"5. 验证IDE和后端都能正常工作")
    
    print(f"\n📋 架构优势:")
    print(f"✅ 元数据与业务数据分离")
    print(f"✅ IDE配置保持不变（连接resource.db）")
    print(f"✅ 性能优化（各司其职）")
    print(f"✅ 维护简单（职责明确）")

def main():
    """主函数"""
    print("🎯 元数据专项迁移 - 架构友好方案")
    print("=" * 60)
    print("📋 方案说明:")
    print("- 仅迁移元数据表到 resource.db")
    print("- 保持业务数据在 fin_data.db")
    print("- 实现数据分层架构")
    print("=" * 60)
    
    # 自动执行迁移
    print("\n🚀 开始执行迁移...")
    
    # 执行迁移步骤
    success = True
    
    # 1. 备份数据库
    backup_databases()
    
    # 2. 迁移元数据表
    if success:
        success = migrate_metadata_tables()
    
    # 3. 验证迁移结果
    if success:
        success = verify_migration()
    
    # 4. 更新配置文件
    if success:
        success = update_env_config()
    
    # 5. 显示后续步骤
    if success:
        show_next_steps()
        print(f"\n🎉 元数据迁移成功完成！")
    else:
        print(f"\n❌ 迁移过程中出现错误，请检查日志")

if __name__ == '__main__':
    main()
