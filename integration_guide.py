#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
集成增强元数据系统到现有Text2SQL服务的指导文档
"""

# 1. 修改 text2sql_service.py 中的提示构建逻辑
def integrate_enhanced_prompts():
    """
    在 chatdb/backend/app/services/text2sql_service.py 中集成增强提示
    """
    
    # 原有代码位置：第26-120行的construct_prompt函数
    # 需要替换为以下增强版本：
    
    enhanced_code = '''
from enhanced_prompt_service import EnhancedPromptService

def construct_prompt(query: str, schema_context: Dict[str, Any], metadata: Dict[str, Any] = None) -> str:
    """构建增强版Text2SQL提示"""
    
    # 使用增强提示服务
    prompt_service = EnhancedPromptService()
    
    # 构建完整的增强提示
    enhanced_prompt = prompt_service.build_enhanced_prompt(query, schema_context)
    
    return enhanced_prompt
    '''
    
    return enhanced_code

# 2. 修改元数据加载逻辑
def integrate_enhanced_metadata():
    """
    在 text2sql_utils.py 中集成增强元数据加载
    """
    
    enhanced_metadata_code = '''
def get_enhanced_financial_metadata(table_name: str = "financial_data") -> Dict[str, Any]:
    """获取增强的财务元数据"""
    
    conn = sqlite3.connect(DATABASE_PATH)
    cursor = conn.cursor()
    
    metadata = {
        "has_metadata": True,
        "table_name": table_name,
        "column_descriptions": [],
        "business_rules": [],
        "field_relationships": [],
        "query_patterns": [],
        "data_quality_rules": []
    }
    
    # 获取增强的字段描述
    cursor.execute("""
        SELECT column_name, chinese_name, description, field_category,
               usage_scenarios, common_values, related_fields, 
               calculation_rules, ai_prompt_hints, business_rules
        FROM column_descriptions
        WHERE table_name = ?
        ORDER BY column_name
    """, (table_name,))
    
    for row in cursor.fetchall():
        metadata["column_descriptions"].append({
            "column_name": row[0],
            "chinese_name": row[1],
            "description": row[2],
            "field_category": row[3],
            "usage_scenarios": row[4],
            "common_values": row[5],
            "related_fields": row[6],
            "calculation_rules": row[7],
            "ai_prompt_hints": row[8],
            "business_rules": row[9]
        })
    
    # 获取业务规则
    cursor.execute("""
        SELECT rule_category, rule_description, sql_example, importance_level
        FROM business_rules
        WHERE table_name = ?
        ORDER BY importance_level DESC
    """, (table_name,))
    
    for row in cursor.fetchall():
        metadata["business_rules"].append({
            "category": row[0],
            "description": row[1],
            "sql_example": row[2],
            "importance": row[3]
        })
    
    # 获取字段关系
    cursor.execute("""
        SELECT primary_field, related_field, relationship_type, 
               relationship_description, usage_example
        FROM field_relationships
        WHERE table_name = ?
    """, (table_name,))
    
    for row in cursor.fetchall():
        metadata["field_relationships"].append({
            "primary_field": row[0],
            "related_field": row[1],
            "relationship_type": row[2],
            "description": row[3],
            "usage_example": row[4]
        })
    
    conn.close()
    return metadata
    '''
    
    return enhanced_metadata_code

# 3. 配置文件更新
def update_configuration():
    """
    更新配置以启用增强元数据功能
    """
    
    config_updates = '''
# 在 chatdb/backend/app/core/config.py 中添加：

# 增强元数据配置
ENABLE_ENHANCED_METADATA: bool = os.getenv("ENABLE_ENHANCED_METADATA", "true").lower() == "true"
ENHANCED_PROMPT_VERSION: str = os.getenv("ENHANCED_PROMPT_VERSION", "v2.0")

# 数据质量检查配置
ENABLE_DATA_QUALITY_CHECK: bool = os.getenv("ENABLE_DATA_QUALITY_CHECK", "true").lower() == "true"
DATA_QUALITY_SEVERITY_LEVEL: str = os.getenv("DATA_QUALITY_SEVERITY_LEVEL", "WARNING")

# AI提示优化配置
AI_PROMPT_TEMPLATE_VERSION: str = os.getenv("AI_PROMPT_TEMPLATE_VERSION", "enhanced")
ENABLE_QUERY_PATTERN_MATCHING: bool = os.getenv("ENABLE_QUERY_PATTERN_MATCHING", "true").lower() == "true"
    '''
    
    return config_updates

# 4. 环境变量设置
def setup_environment_variables():
    """
    设置环境变量以启用增强功能
    """
    
    env_vars = '''
# 在 .env 文件中添加：

# 增强元数据功能
ENABLE_ENHANCED_METADATA=true
ENHANCED_PROMPT_VERSION=v2.0

# 数据质量检查
ENABLE_DATA_QUALITY_CHECK=true
DATA_QUALITY_SEVERITY_LEVEL=WARNING

# AI提示优化
AI_PROMPT_TEMPLATE_VERSION=enhanced
ENABLE_QUERY_PATTERN_MATCHING=true

# 调试模式（可选）
DEBUG_ENHANCED_PROMPTS=false
LOG_PROMPT_DETAILS=false
    '''
    
    return env_vars

# 5. 数据库迁移脚本
def create_migration_script():
    """
    创建数据库迁移脚本
    """
    
    migration_script = '''
# 创建 alembic 迁移文件：
# alembic revision -m "add_enhanced_metadata_tables"

"""add enhanced metadata tables

Revision ID: enhanced_metadata_001
Revises: previous_revision
Create Date: 2024-01-01 00:00:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers
revision = 'enhanced_metadata_001'
down_revision = 'previous_revision'
branch_labels = None
depends_on = None

def upgrade():
    # 添加字段到现有表
    op.add_column('column_descriptions', sa.Column('field_category', sa.Text(), nullable=True))
    op.add_column('column_descriptions', sa.Column('usage_scenarios', sa.Text(), nullable=True))
    op.add_column('column_descriptions', sa.Column('common_values', sa.Text(), nullable=True))
    op.add_column('column_descriptions', sa.Column('related_fields', sa.Text(), nullable=True))
    op.add_column('column_descriptions', sa.Column('calculation_rules', sa.Text(), nullable=True))
    op.add_column('column_descriptions', sa.Column('ai_prompt_hints', sa.Text(), nullable=True))
    
    # 创建新表
    op.create_table('field_relationships',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('table_name', sa.String(255), nullable=False),
        sa.Column('primary_field', sa.String(255), nullable=False),
        sa.Column('related_field', sa.String(255), nullable=False),
        sa.Column('relationship_type', sa.String(100), nullable=False),
        sa.Column('relationship_description', sa.Text(), nullable=True),
        sa.Column('usage_example', sa.Text(), nullable=True),
        sa.Column('created_at', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    
    # 其他表的创建...

def downgrade():
    # 回滚操作
    op.drop_table('field_relationships')
    op.drop_column('column_descriptions', 'ai_prompt_hints')
    # 其他回滚操作...
    '''
    
    return migration_script

# 6. 测试验证脚本
def create_validation_tests():
    """
    创建验证测试脚本
    """
    
    test_script = '''
import pytest
from enhanced_prompt_service import EnhancedPromptService

class TestEnhancedMetadata:
    
    def setup_method(self):
        self.prompt_service = EnhancedPromptService()
    
    def test_enhanced_metadata_loading(self):
        """测试增强元数据加载"""
        metadata = self.prompt_service.get_enhanced_metadata()
        
        assert 'column_descriptions' in metadata
        assert 'business_rules' in metadata
        assert 'field_relationships' in metadata
        assert len(metadata['column_descriptions']) > 0
    
    def test_enhanced_prompt_generation(self):
        """测试增强提示生成"""
        schema_context = {
            'tables': [{
                'name': 'financial_data',
                'columns': [
                    {'column_name': 'credit_amount', 'data_type': 'REAL'}
                ]
            }]
        }
        
        prompt = self.prompt_service.build_enhanced_prompt(
            "查询收入情况", schema_context
        )
        
        assert '财务数据查询核心规则' in prompt
        assert 'credit_amount' in prompt
        assert '收入类科目' in prompt
    
    def test_query_pattern_matching(self):
        """测试查询模式匹配"""
        patterns = self.prompt_service.get_query_patterns_prompt("收入分析")
        
        assert '收入分析模式' in patterns or patterns == ""
    '''
    
    return test_script

if __name__ == "__main__":
    print("🔧 增强元数据系统集成指南")
    print("=" * 50)
    print("1. 更新Text2SQL服务")
    print("2. 集成增强元数据加载")
    print("3. 更新配置文件")
    print("4. 设置环境变量")
    print("5. 运行数据库迁移")
    print("6. 执行验证测试")
    print("\n详细代码请查看各个函数的返回内容。")
