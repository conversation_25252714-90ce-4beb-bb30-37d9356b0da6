-- ================================================================================
-- FINANCIAL_DATA 表元数据查询脚本
-- ================================================================================
-- 本脚本包含了查询 financial_data 表描述信息的各种SQL语句
-- 可以帮助AI模型理解表结构和业务规则

-- 1. 查询表级别描述信息
-- ================================================================================
SELECT 
    table_name as "表名",
    description as "表描述", 
    business_purpose as "业务用途",
    data_scale as "数据规模",
    created_at as "创建时间"
FROM table_descriptions 
WHERE table_name = 'financial_data';

-- 2. 查询所有字段的详细描述
-- ================================================================================
SELECT 
    column_name as "字段名",
    chinese_name as "中文名",
    description as "字段描述",
    data_type as "数据类型",
    business_rules as "业务规则",
    ai_understanding_points as "AI理解要点"
FROM column_descriptions 
WHERE table_name = 'financial_data'
ORDER BY 
    CASE 
        WHEN column_name IN ('year', 'month') THEN 1
        WHEN column_name LIKE 'accounting_%' THEN 2
        WHEN column_name LIKE 'account_%' THEN 3
        WHEN column_name LIKE '%amount%' OR column_name LIKE '%cumulative%' OR column_name = 'balance' THEN 4
        WHEN column_name LIKE 'project_%' THEN 5
        WHEN column_name LIKE 'bank_%' OR column_name LIKE 'financial_institution_%' THEN 6
        ELSE 7
    END,
    column_name;

-- 3. 按字段类别查询描述信息
-- ================================================================================

-- 3.1 时间维度字段
SELECT 
    column_name as "字段名",
    chinese_name as "中文名", 
    description as "描述",
    ai_understanding_points as "AI理解要点"
FROM column_descriptions 
WHERE table_name = 'financial_data' 
    AND column_name IN ('year', 'month')
ORDER BY column_name;

-- 3.2 组织架构字段
SELECT 
    column_name as "字段名",
    chinese_name as "中文名",
    description as "描述", 
    ai_understanding_points as "AI理解要点"
FROM column_descriptions 
WHERE table_name = 'financial_data' 
    AND column_name LIKE 'accounting_%'
ORDER BY column_name;

-- 3.3 会计科目字段
SELECT 
    column_name as "字段名",
    chinese_name as "中文名",
    description as "描述",
    ai_understanding_points as "AI理解要点"
FROM column_descriptions 
WHERE table_name = 'financial_data' 
    AND column_name LIKE 'account_%'
ORDER BY column_name;

-- 3.4 金额字段（核心财务数据）
SELECT 
    column_name as "字段名",
    chinese_name as "中文名",
    description as "描述",
    data_type as "数据类型",
    ai_understanding_points as "AI理解要点"
FROM column_descriptions 
WHERE table_name = 'financial_data' 
    AND (column_name LIKE '%amount%' OR column_name LIKE '%cumulative%' OR column_name = 'balance')
ORDER BY column_name;

-- 3.5 项目管理字段
SELECT 
    column_name as "字段名",
    chinese_name as "中文名",
    description as "描述",
    ai_understanding_points as "AI理解要点"
FROM column_descriptions 
WHERE table_name = 'financial_data' 
    AND column_name LIKE 'project_%'
ORDER BY column_name;

-- 3.6 银行信息字段
SELECT 
    column_name as "字段名",
    chinese_name as "中文名",
    description as "描述",
    ai_understanding_points as "AI理解要点"
FROM column_descriptions 
WHERE table_name = 'financial_data' 
    AND (column_name LIKE 'bank_%' OR column_name LIKE 'financial_institution_%')
ORDER BY column_name;

-- 4. 查询关键业务规则
-- ================================================================================
SELECT 
    rule_category as "规则类别",
    rule_description as "规则描述",
    sql_example as "SQL示例",
    importance_level as "重要级别"
FROM business_rules 
WHERE table_name = 'financial_data'
ORDER BY 
    CASE importance_level 
        WHEN 'CRITICAL' THEN 1 
        WHEN 'HIGH' THEN 2 
        WHEN 'MEDIUM' THEN 3 
        ELSE 4 
    END,
    rule_category;

-- 5. 使用元数据视图查询
-- ================================================================================

-- 5.1 查询表元数据
SELECT * FROM financial_data_with_metadata;

-- 5.2 查询字段元数据
SELECT * FROM financial_data_columns_metadata;

-- 6. 实际业务查询示例（基于业务规则）
-- ================================================================================

-- 6.1 资产类科目余额汇总（使用balance字段）
SELECT 
    accounting_unit_name as "核算单位",
    SUM(CAST(balance AS REAL)) as "资产总额"
FROM financial_data
WHERE account_code LIKE '1%' 
    AND year = 2024 
    AND month = 9
GROUP BY accounting_unit_name
ORDER BY "资产总额" DESC;

-- 6.2 收入类科目汇总（使用credit_amount字段）
SELECT 
    accounting_unit_name as "核算单位",
    SUM(credit_amount) as "当期收入",
    SUM(credit_cumulative) as "累计收入"
FROM financial_data
WHERE account_code LIKE '60%' 
    AND year = 2024 
    AND month = 9
GROUP BY accounting_unit_name
ORDER BY "当期收入" DESC;

-- 6.3 成本费用类科目汇总（使用debit_amount字段）
SELECT 
    accounting_unit_name as "核算单位",
    SUM(debit_amount) as "当期费用",
    SUM(debit_cumulative) as "累计费用"
FROM financial_data
WHERE (account_code LIKE '64%' OR account_code LIKE '66%')
    AND year = 2024 
    AND month = 9
GROUP BY accounting_unit_name
ORDER BY "当期费用" DESC;

-- 7. 数据质量检查查询
-- ================================================================================

-- 7.1 检查借贷平衡
SELECT 
    year,
    month,
    SUM(debit_amount) as "借方总额",
    SUM(credit_amount) as "贷方总额",
    ABS(SUM(debit_amount) - SUM(credit_amount)) as "差额"
FROM financial_data
WHERE year = 2024 AND month = 9
GROUP BY year, month;

-- 7.2 检查科目方向一致性
SELECT 
    account_direction as "科目方向",
    COUNT(*) as "记录数",
    AVG(CAST(balance AS REAL)) as "平均余额"
FROM financial_data
WHERE balance IS NOT NULL AND balance != ''
GROUP BY account_direction;

-- 8. 综合元数据查询
-- ================================================================================
-- 获取完整的表结构信息，包括字段描述和业务规则
SELECT 
    'TABLE_INFO' as info_type,
    table_name as name,
    description as description,
    business_purpose as details,
    NULL as importance
FROM table_descriptions 
WHERE table_name = 'financial_data'

UNION ALL

SELECT 
    'COLUMN_INFO' as info_type,
    column_name as name,
    chinese_name || ': ' || description as description,
    ai_understanding_points as details,
    NULL as importance
FROM column_descriptions 
WHERE table_name = 'financial_data'

UNION ALL

SELECT 
    'BUSINESS_RULE' as info_type,
    rule_category as name,
    rule_description as description,
    sql_example as details,
    importance_level as importance
FROM business_rules 
WHERE table_name = 'financial_data'

ORDER BY 
    CASE info_type 
        WHEN 'TABLE_INFO' THEN 1 
        WHEN 'COLUMN_INFO' THEN 2 
        WHEN 'BUSINESS_RULE' THEN 3 
    END,
    name;
