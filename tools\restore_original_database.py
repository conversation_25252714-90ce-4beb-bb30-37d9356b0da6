#!/usr/bin/env python3
"""
恢复原始数据库 - 将balance字段恢复为TEXT类型

作者: AI Assistant
日期: 2025-06-26
"""

import sqlite3
import shutil
from datetime import datetime
import os


def restore_database(backup_file='fin_data.db.backup_20250626_073153', target_db='fin_data.db'):
    """恢复数据库到修改前的状态"""
    
    # 检查备份文件是否存在
    if not os.path.exists(backup_file):
        print(f"❌ 错误: 备份文件 {backup_file} 不存在")
        return False
    
    try:
        # 创建当前状态的备份（以防需要回滚）
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        current_backup = f"{target_db}.before_restore_{timestamp}"
        if os.path.exists(target_db):
            shutil.copy2(target_db, current_backup)
            print(f"当前数据库已备份到: {current_backup}")
        
        # 恢复原始数据库
        print(f"正在从 {backup_file} 恢复数据库...")
        shutil.copy2(backup_file, target_db)
        print(f"✅ 数据库已恢复到: {target_db}")
        
        # 验证恢复结果
        print("验证恢复结果...")
        conn = sqlite3.connect(target_db)
        cursor = conn.cursor()
        
        # 检查表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='financial_data'")
        table_exists = cursor.fetchone()
        
        if not table_exists:
            print("❌ 错误: financial_data表不存在")
            return False
        
        # 检查balance字段类型
        cursor.execute("PRAGMA table_info(financial_data)")
        columns = cursor.fetchall()
        balance_col = next((col for col in columns if col[1] == 'balance'), None)
        
        if balance_col:
            print(f"✅ Balance字段类型: {balance_col[2]}")
            if balance_col[2] == 'TEXT':
                print("✅ Balance字段已恢复为TEXT类型")
            else:
                print(f"⚠️  警告: Balance字段类型为 {balance_col[2]}，不是预期的TEXT")
        else:
            print("❌ 错误: 未找到balance字段")
            return False
        
        # 检查数据行数
        cursor.execute("SELECT COUNT(*) FROM financial_data")
        row_count = cursor.fetchone()[0]
        print(f"✅ 数据行数: {row_count:,}")
        
        # 检查balance字段的示例数据
        cursor.execute("SELECT balance FROM financial_data WHERE balance != '0.00000000' LIMIT 5")
        sample_data = cursor.fetchall()
        print("示例balance数据:")
        for row in sample_data:
            print(f"  {row[0]} (类型: {type(row[0])})")
        
        conn.close()
        
        print("\n=== 恢复完成 ===")
        print(f"✅ 数据库已成功恢复到修改前的状态")
        print(f"✅ Balance字段类型: TEXT")
        print(f"✅ 数据完整性验证通过")
        print(f"✅ 当前状态备份: {current_backup}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 恢复失败: {str(e)}")
        
        # 如果恢复失败，尝试恢复当前状态备份
        if 'current_backup' in locals() and os.path.exists(current_backup):
            try:
                shutil.copy2(current_backup, target_db)
                print(f"已恢复到恢复前状态: {current_backup}")
            except:
                print("无法恢复到恢复前状态")
        
        return False


def verify_original_state(db_path='fin_data.db'):
    """验证数据库是否恢复到原始状态"""
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("=== 原始状态验证 ===")
        
        # 检查表结构
        cursor.execute("PRAGMA table_info(financial_data)")
        columns = cursor.fetchall()
        
        print(f"表结构验证:")
        print(f"  总列数: {len(columns)}")
        
        # 重点检查balance字段
        balance_col = next((col for col in columns if col[1] == 'balance'), None)
        if balance_col:
            print(f"  Balance字段: {balance_col[1]} {balance_col[2]}")
            if balance_col[2] == 'TEXT':
                print("  ✅ Balance字段类型正确 (TEXT)")
            else:
                print(f"  ❌ Balance字段类型错误: {balance_col[2]}")
        
        # 检查其他金额字段类型
        amount_fields = ['debit_amount', 'credit_amount', 'debit_cumulative', 'credit_cumulative', 'opening_debit_amount']
        print(f"  其他金额字段类型:")
        for col in columns:
            if col[1] in amount_fields:
                print(f"    {col[1]}: {col[2]}")
        
        # 检查数据
        cursor.execute("SELECT COUNT(*) FROM financial_data")
        total_rows = cursor.fetchone()[0]
        print(f"  数据行数: {total_rows:,}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"验证失败: {str(e)}")
        return False


if __name__ == "__main__":
    print("开始恢复原始数据库...")
    
    success = restore_database()
    
    if success:
        print("\n进行最终验证...")
        verify_original_state()
        print("\n🎉 数据库恢复成功!")
        print("您的原有功能现在应该可以正常工作了。")
    else:
        print("\n💥 数据库恢复失败")
