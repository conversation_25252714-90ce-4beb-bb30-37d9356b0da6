#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os

def verify_database_status():
    """验证当前数据库状态"""
    print('🔍 验证当前数据库状态')
    print('=' * 60)

    # 检查resource.db
    resource_db = 'chatdb/backend/resource.db'
    print(f'\n📊 检查 {resource_db}:')
    if os.path.exists(resource_db):
        conn = sqlite3.connect(resource_db)
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        print(f'   表数量: {len(tables)}')
        print(f'   表列表: {tables}')
        
        # 检查关键表
        key_tables = ['financial_data', 'table_descriptions', 'column_descriptions', 'business_rules']
        for table in key_tables:
            if table in tables:
                cursor.execute(f'SELECT COUNT(*) FROM {table}')
                count = cursor.fetchone()[0]
                print(f'   ✅ {table}: {count:,} 条记录')
            else:
                print(f'   ❌ {table}: 不存在')
        conn.close()
    else:
        print('   ❌ 文件不存在')

    # 检查fin_data.db
    fin_data_db = 'fin_data.db'
    print(f'\n📊 检查 {fin_data_db}:')
    if os.path.exists(fin_data_db):
        conn = sqlite3.connect(fin_data_db)
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        print(f'   表数量: {len(tables)}')
        print(f'   表列表: {tables}')
        
        # 检查关键表
        key_tables = ['financial_data', 'table_descriptions', 'column_descriptions', 'business_rules']
        for table in key_tables:
            if table in tables:
                cursor.execute(f'SELECT COUNT(*) FROM {table}')
                count = cursor.fetchone()[0]
                print(f'   ✅ {table}: {count:,} 条记录')
            else:
                print(f'   ❌ {table}: 不存在')
        conn.close()
    else:
        print('   ❌ 文件不存在')

    # 检查配置文件
    env_file = 'chatdb/backend/.env'
    print(f'\n⚙️ 检查配置文件 {env_file}:')
    if os.path.exists(env_file):
        with open(env_file, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                if 'SQLITE_DB_PATH' in line:
                    print(f'   第{line_num}行: {line.strip()}')
    else:
        print('   ❌ 配置文件不存在')

if __name__ == '__main__':
    verify_database_status()
