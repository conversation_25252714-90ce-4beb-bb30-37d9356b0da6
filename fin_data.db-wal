7� -�       H]�6�o��D[.��6    p/H]�6�o�ދG>=�SQLite format 3   @      p/  ��  �   )                                                  .n�
   
v �L
v�
�nDQ�                                                                                                                                                                                                                 �p
KK�[viewfinancial_data_columns_metadatafinancial_data_columns_metadataCREATE VIEW financial_data_columns_metadata AS
            SELECT 
                column_name,
                chinese_name,
                description,
                data_type,
                business_rules,
                ai_understanding_points
            FROM column_descriptions
            WHERE table_name = 'financial_data'
            ORDER BY 
                CASE 
                    WHEN column_name IN ('year', 'month') THEN 1
                    WHEN column_name LIKE 'accounting_%' THEN 2
                    WHEN column_name LIKE 'account_%' THEN 3
                    WHEN column_name LIKE '%amount%' OR column_name LIKE '%cumulative%' OR column_name = 'balance' THEN 4
                    WHEN column_name LIKE 'project_%' THEN 5
                    WHEN column_name LIKE 'bank_%' OR column_name LIKE 'financial_institution_%' THEN 6
                    ELSE 7
                END,
                column_name�S	EE�-viewfinancial_data_with_metadatafinancial_data_with_metadataCREATE VIEW financial_data_with_metadata AS
            SELECT 
                'financial_data' as table_name,
                td.description as table_description,
                td.business_purpose,
                td.data_scale,
                (SELECT COUNT(*) FROM financial_data) as current_record_count
            FROM table_descriptions td
            WHERE td.table_name = 'financial_data'R++Ytablesqlite_sequencesqlite_sequence ��CREATE TABLE sqlite_sequence(name,seq)�*))�tablebusiness_rulesbusiness_rules ��CREATE TABLE business_rules (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                table_name TEXT NOT NULL,
                rule_category TEXT NOT NULL,
                rule_description TEXT NOT NULL,
                sql_example TEXT,
                importance_level TEXT DEFAULT 'HIGH',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )�(33�stablecolumn_descriptionscolumn_descriptions ��CREATE TABLE column_descriptions (
                table_name TEXT NOT NULL,
                column_name TEXT NOT NULL,
                chinese_name TEXT,
                description TEXT,
                data_type TEXT,
                business_rules TEXT,
                ai_understanding_�S	EE�-viewfinancial_data_with_metadatafinancial_data_with_metadataCREATE VIEW financial_data_with_metadata AS
            SELECT 
                'financial_data' as table_name,
                td.description as table_description,
                td.business_purpose,
                td.data_scale,
                (SELECT COUNT(*) FROM financial_data) as current_record_count
            FROM table_descriptions td
            WHERE td.table_name = 'financial_data'R++Ytablesqlite_sequencesqlite_sequence ��CREATE TABLE sqlite_sequence(name,seq)�]))�qtablefinancial_datafinancial_data �CREATE TABLE "financial_data" ("year" INTEGER, "month" INTEGER, "accounting_organization" INTEGER, "accounting_unit_name" TEXT, "account_code" INTEGER, "account_full_name" TEXT, "account_name" TEXT, "opening_debit_amount" REAL, "opening_credit_amount" INTEGER, "account_direction" TEXT, "project_id" TEXT, "project_code" TEXT, "project_name" TEXT, "market_nature_id" TEXT, "tax_rate_id" TEXT, "tax_rate_name" TEXT, "business_format_id" TEXT, "financial_product_id" TEXT, "long_term_deferred_project_id" TEXT, "property_unit_id" TEXT, "cash_flow_project_id" TEXT, "municipal_enterprise_unit_id" TEXT, "bank_account_id" TEXT, "financial_institution_id" INTEGER, "bank_routing_number" INTEGER, "bank_name" TEXT, "debit_amount" REAL, "debit_cumulative" REAL, "credit_amount" REAL, "credit_cumulative" REAL, "balance" TEXT)    p/H]�6�o���q�"şASQLite format 3   @      p/  ��  �   *                                                  .n�
   � �L
v�
�nDQ�                                                                                                                                                                                                                 �p
KK�[viewfinancial_data_columns_metadatafinancial_data_columns_metadataCREATE VIEW financial_data_columns_metadata AS
            SELECT 
                column_name,
                chinese_name,
                description,
                data_type,
                business_rules,
                ai_understanding_points
            FROM column_descriptions
            WHERE table_name = 'financial_data'
            ORDER BY 
                CASE 
                    WHEN column_name IN ('year', 'month') THEN 1
                    WHEN column_name LIKE 'accounting_%' THEN 2
                    WHEN column_name LIKE 'account_%' THEN 3
                    WHEN column_name LIKE '%amount%' OR column_name LIKE '%cumulative%' OR column_name = 'balance' THEN 4
                    WHEN column_name LIKE 'project_%' THEN 5
                    WHEN column_name LIKE 'bank_%' OR column_name LIKE 'financial_institution_%' THEN 6
                    ELSE 7
                END,
                column_name�S	EE�-viewfinancial_data_with_metadatafinancial_data_with_metadataCREATE VIEW financial_data_with_metadata AS
            SELECT 
                'financial_data' as table_name,
                td.description as table_description,
                td.business_purpose,
                td.data_scale,
                �p
KK�[viewfinancial_data_columns_metadatafinancial_data_columns_metadataCREATE VIEW financial_data_columns_metadata AS
            SELECT 
                column_name,
                chinese_name,
                description,
                data_type,
                business_rules,
                ai_understanding_points
            FROM column_descriptions
            WHERE table_name = 'financial_data'
            ORDER BY 
                CASE 
                    WHEN column_name IN ('year', 'month') THEN 1
                    WHEN column_name LIKE 'accounting_%' THEN 2
                    WHEN column_name LIKE 'account_%' THEN 3
                    WHEN column_name LIKE '%amount%' OR column_name LIKE '%cumulative%' OR column_name = 'balance' THEN 4
                    WHEN column_name LIKE 'project_%' THEN 5
                    WHEN column_name LIKE 'bank_%' OR column_name LIKE 'financial_institution_%' THEN 6
                    ELSE 7
                END,
                column_name�S	EE�-viewfinancial_data_with_metadatafinancial_data_with_metadataCREATE VIEW financial_data_with_metadata AS
            SELECT 
                'financial_data' as table_name,
                td.description as table_description,
                td.business_purpose,
                td.data_scale,
                (SELECT COUNT(*) FROM financial_data) as current_record_count
            FROM table_descriptions td
            WHERE td.table_name = 'financial_data'R++Ytablesqlite_sequencesqlite_sequence ��CREATE TABLE sqlite_sequence(name,seq)�]))�qtablefinancial_datafinancial_data �CREATE TABLE "financial_data" ("year" INTEGER, "month" INTEGER, "accounting_organization" INTEGER, "accounting_unit_name" TEXT, "account_code" INTEGER, "account_full_name" TEXT, "account_name" TEXT, "opening_debit_amount" REAL, "opening_credit_amount" INTEGER, "account_direction" TEXT, "project_id" TEXT, "project_code" TEXT, "project_name" TEXT, "market_nature_id" TEXT, "tax_rate_id" TEXT, "tax_rate_name" TEXT, "business_format_id" TEXT, "financial_product_id" TEXT, "long_term_deferred_project_id" TEXT, "property_unit_id" TEXT, "cash_flow_project_id" TEXT, "municipal_enterprise_unit_id" TEXT, "bank_account_id" TEXT, "financial_institution_id" INTEGER, "bank_routing_number" INTEGER, "bank_name" TEXT, "debit_amount" REAL, "debit_cumulative" REAL, "credit_amount" REAL, "credit_cumulative" REAL, "balance" TEXT)       H]�6�o�5�ˠ���SQLite format 3   @      p/  ��  �   +                                                  .n�
{ � �L
v��4DQ�                                                                                                                                                                                                                 �p
KK�[viewfinancial_data_columns_metadatafinancial_data_columns_metadataCREATE VIEW financial_data_columns_metadata AS
            SELECT 
                column_name,
                chinese_name,
                description,
                data_type,
                business_rules,
                ai_understanding_points
            FROM column_descriptions
            WHERE table_name = 'financial_data'
            ORDER BY 
                CASE 
                    WHEN column_name IN ('year', 'month') THEN 1
                    WHEN column_name LIKE 'accounting_%' THEN 2
                    WHEN column_name LIKE 'account_%' THEN 3
                    WHEN column_name LIKE '%amount%' OR column_name LIKE '%cumulative%' OR column_name = 'balance' THEN 4
                    WHEN column_name LIKE 'project_%' THEN 5
                    WHEN column_name LIKE 'bank_%' OR column_name LIKE 'financial_institution_%' �?11�)tabletable_descriptionstable_descriptionsCREATE TABLE table_descriptions (
                table_name TEXT PRIMARY KEY,
                description TEXT NOT NULL,
                business_purpose TEXT,
                data_scale TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )EW1 indexsqlite_autoindex_table_descriptions_1table_descriptions ��       �p
KK�[viewfinancial_data_columns_metadatafinancial_data_columns_metadataCREATE VIEW financial_data_columns_metadata AS
            SELECT 
                column_name,
                chinese_name,
                description,
                data_type,
                business_rules,
                ai_understanding_points
            FROM column_descriptions
            WHERE table_name = 'financial_data'
            ORDER BY 
                CASE 
                    WHEN column_name IN ('year', 'month') THEN 1
                    WHEN column_name LIKE 'accounting_%' THEN 2
                    WHEN column_name LIKE 'account_%' THEN 3
                    WHEN column_name LIKE '%amount%' OR column_name LIKE '%cumulative%' OR column_name = 'balance' THEN 4
                    WHEN column_name LIKE 'project_%' THEN 5
                    WHEN column_name LIKE 'bank_%' OR column_name LIKE 'financial_institution_%' THEN 6
                    ELSE 7
                END,
                column_name�S	EE�-viewfinancial_data_with_metadatafinancial_data_with_metadataCREATE VIEW financial_data_with_metadata AS
            SELECT 
                'financial_data' as table_name,
                td.description as table_description,
                td.business_purpose,
                td.data_scale,
                (SELECT COUNT(*) FROM financial_data) as current_record_count
            FROM table_descriptions td
            WHERE td.table_name = 'financial_data'R++Ytablesqlite_sequencesqlite_sequence ��CREATE TABLE sqlite_sequence(name,seq)�]))�qtablefinancial_datafinancial_data �CREATE TABLE "financial_data" ("year" INTEGER, "month" INTEGER, "accounting_organization" INTEGER, "accounting_unit_name" TEXT, "account_code" INTEGER, "account_full_name" TEXT, "account_name" TEXT, "opening_debit_amount" REAL, "opening_credit_amount" INTEGER, "account_direction" TEXT, "project_id" TEXT, "project_code" TEXT, "project_name" TEXT, "market_nature_id" TEXT, "tax_rate_id" TEXT, "tax_rate_name" TEXT, "business_format_id" TEXT, "financial_product_id" TEXT, "long_term_deferred_project_id" TEXT, "property_unit_id" TEXT, "cash_flow_project_id" TEXT, "municipal_enterprise_unit_id" TEXT, "bank_account_id" TEXT, "financial_institution_id" INTEGER, "bank_routing_number" INTEGER, "bank_name" TEXT, "debit_amount" REAL, "debit_cumulative" REAL, "credit_amount" REAL, "credit_cumulative" REAL, "balance" TEXT)       H]�6�o�+��n�ݷ
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                ��    H]�6�o�a,�&�'  ��  U  �  �  �  �  �  �  �  �  �  �  �  �
  �  �  �
  �	  �  �  �  �  �  �  �  �  �   ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  �   �  �  �  �  �  �  �  �  �	  �
  �  �  �
  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �   �!  �"  �#  �$  �%  �&  �'  �(  �)  �*  �+  �,  �-  �.  �/  �0  �1  �2  �3  �4  �5  �6  �7  �8  �9  �:  �;  �<  �=  �>  �?  �@  �A  �B  �C  �D  �E  �F  �G  �H  �I  �J  �K  �L  �M  �N  �O  �P  �Q  �R  �S  �T  �U  �V  �W  �X  �Y  �Z  �[  �\  �]  �^  �_  �`  �a  �b  �c  �d  �e  �f  �g  �h  �i  �j  �k  �l  �n  �o  �p  �q  �r  �s  �t  �u  �v  �w  �x  �y  �z  �{  �|  �}  �~  �  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  �m  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��   行存款借       79035880-87f3-4a3b-ae13-6ab820525793 49777ac1-2845-8af5-1789-54cb4a72c850 G�
0� G�
0�招商银行股份有限公司深圳分行票据业务部�-4480.00000000�B��!�;%%


UU+�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       f5e52502-a987-41cd-9628-9e72ce7e508f 49777ac1-2845-8af5-1789-54cb4a72c850 G�
0� G�
0�招商银行股份有限公司深圳分行票据业务部p�-28879.00000000�D��!�;%%


UU#�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       7604f734-3f8a-4767-a39e-08183d22cb35 49777ac1-2845-8af5-1789-54cb4a72c850 G�
0� G�
0�招商银行股份有限公司深圳分行票据业务部@Iٙ����51.70000000�I��!�;%%


UU-�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       a4be09b9-c44f-4194-a848-2d1aa0d1911c 49777ac1-2845-8af5-1789-54cb4a72c850 G�
0� G�
0�招商银行股份有限公司深圳分行票据业务部A������-340922.95000000�,��!�;%%


UUO-�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       f5e52502-a987-41cd-9628-9e72ce7e508f 49777ac1-2845-8af5-1789-54cb4a72c850 G�
. G�
.招商银行深圳分行营业部T�-152735.00000000�/��!�;%%


UUO/�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       79035880-87f3-4a3b-ae13-6ab820525793 49777ac1-2845-8af5-1789-54cb4a72c850 G�
. G�
.招商银行深圳分行营业部 XX�-1325136.00000000�0��!�;%%


UUO)�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       5ec52f35-fa80-4795-9f3d-df0dd5006c6a 49777ac1-2845-8af5-1789-54cb4a72c850 G�
. G�
.招商银行深圳分行营业部@�Y�\)-1789.49000000�8��!�;%%


UUO+�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       3465b495-bfa8-41bd-99fe-49e8efb61e26 49777ac1-2845-8af5-1789-54cb4a72c850 G�
. G�
.招商银行深圳分行营业部A(۞z�H�ATz�G�814577.70000000�-��!�;%%


UUO%�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       7604f734-3f8a-4767-a39e-08183d22cb35 49777ac1-2845-8af5-1789-54cb4a72c850 G�
. G�
.招商银行深圳分行营业部@nz�G�240.19000000  �� p/H]�6�o�ˮO�.�Gg
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     H]�6�o����1R�SQLite format 3   @      p/  ��  �   ,                                                  .n�
{ � �L
v��4��Q�                                                                                                                                                                                                                 �p
KK�[viewfinancial_data_columns_metadatafinancial_data_columns_metadataCREATE VIEW financial_data_columns_metadata AS
            SELECT 
                column_name,
                chinese_name,
                description,
                data_type,
                business_rules,
                ai_understanding_points
            FROM column_descriptions
            WHERE table_name = 'financial_data'
            OR�(
33�stablecolumn_descriptionscolumn_descriptions ��CREATE TABLE column_descriptions (
                table_name TEXT NOT NULL,
                column_name TEXT NOT NULL,
                chinese_name TEXT,
                description TEXT,
                data_type TEXT,
                business_rules TEXT,
                ai_understanding_points TEXT,
                PRIMARY KEY (table_name, column_name)
            )GY3 indexsqlite_autoindex_column_descriptions_1column_descriptions �Ă?11�)tabletable_descriptionstable_descriptionsCREATE TABLE table_descriptions (
                table_name TEXT PRIMARY KEY,
                description TEXT NOT NULL,
                business_purpose TEXT,
                data_scale TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )EW1 indexsqlite_autoindex_table_descriptions_1table_descriptions ��       �p
KK�[viewfinancial_data_columns_metadatafinancial_data_columns_metadataCREATE VIEW financial_data_columns_metadata AS
            SELECT 
                column_name,
                chinese_name,
                description,
                data_type,
                business_rules,
                ai_understanding_points
            FROM column_descriptions
            WHERE table_name = 'financial_data'
            ORDER BY 
                CASE 
                    WHEN column_name IN ('year', 'month') THEN 1
                    WHEN column_name LIKE 'accounting_%' THEN 2
                    WHEN column_name LIKE 'account_%' THEN 3
                    WHEN column_name LIKE '%amount%' OR column_name LIKE '%cumulative%' OR column_name = 'balance' THEN 4
                    WHEN column_name LIKE 'project_%' THEN 5
                    WHEN column_name LIKE 'bank_%' OR column_name LIKE 'financial_institution_%' THEN 6
                    ELSE 7
                END,
                column_name�S	EE�-viewfinancial_data_with_metadatafinancial_data_with_metadataCREATE VIEW financial_data_with_metadata AS
            SELECT 
                'financial_data' as table_name,
                td.description as table_description,
                td.business_purpose,
                td.data_scale,
                (SELECT COUNT(*) FROM financial_data) as current_record_count
            FROM table_descriptions td
            WHERE td.table_name = 'financial_data'R++Ytablesqlite_sequencesqlite_sequence ��CREATE TABLE sqlite_sequence(name,seq)�]))�qtablefinancial_datafinancial_data �CREATE TABLE "financial_data" ("year" INTEGER, "month" INTEGER, "accounting_organization" INTEGER, "accounting_unit_name" TEXT, "account_code" INTEGER, "account_full_name" TEXT, "account_name" TEXT, "opening_debit_amount" REAL, "opening_credit_amount" INTEGER, "account_direction" TEXT, "project_id" TEXT, "project_code" TEXT, "project_name" TEXT, "market_nature_id" TEXT, "tax_rate_id" TEXT, "tax_rate_name" TEXT, "business_format_id" TEXT, "financial_product_id" TEXT, "long_term_deferred_project_id" TEXT, "property_unit_id" TEXT, "cash_flow_project_id" TEXT, "municipal_enterprise_unit_id" TEXT, "bank_account_id" TEXT, "financial_institution_id" INTEGER, "bank_routing_number" INTEGER, "bank_name" TEXT, "debit_amount" REAL, "debit_cumulative" REAL, "credit_amount" REAL, "credit_cumulative" REAL, "balance" TEXT)  ��    H]�6�o�:����ߘ�  ��  S  �  �  �  �  �  �  �  �  �  �  �  �
  �  �  �
  �	  �  �  �  �  �  �  �  �  �   ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  �   �  �  �  �  �  �  �  �  �	  �
  �  �  �
  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �   �!  �"  �#  �$  �%  �&  �'  �(  �)  �*  �+  �,  �-  �.  �/  �0  �1  �2  �3  �4  �5  �6  �7  �8  �9  �:  �;  �<  �=  �>  �?  �@  �A  �B  �C  �D  �E  �F  �G  �H  �I  �J  �K  �L  �M  �N  �O  �P  �Q  �R  �S  �T  �U  �V  �W  �X  �Y  �Z  �[  �\  �]  �^  �_  �`  �a  �b  �c  �d  �e  �f  �g  �h  �i  �j  �k  �l  �n  �o  �p  �q  �r  �s  �t  �u  �v  �w  �x  �y  �z  �{  �|  �}  �~  �  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  �m  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��   行存款借       79035880-87f3-4a3b-ae13-6ab820525793 49777ac1-2845-8af5-1789-54cb4a72c850 G�
0� G�
0�招商银行股份有限公司深圳分行票据业务部�-4480.00000000�B��!�;%%


UU+�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       f5e52502-a987-41cd-9628-9e72ce7e508f 49777ac1-2845-8af5-1789-54cb4a72c850 G�
0� G�
0�招商银行股份有限公司深圳分行票据业务部p�-28879.00000000�D��!�;%%


UU#�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       7604f734-3f8a-4767-a39e-08183d22cb35 49777ac1-2845-8af5-1789-54cb4a72c850 G�
0� G�
0�招商银行股份有限公司深圳分行票据业务部@Iٙ����51.70000000�I��!�;%%


UU-�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       a4be09b9-c44f-4194-a848-2d1aa0d1911c 49777ac1-2845-8af5-1789-54cb4a72c850 G�
0� G�
0�招商银行股份有限公司深圳分行票据业务部A������-340922.95000000�,��!�;%%


UUO-�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       f5e52502-a987-41cd-9628-9e72ce7e508f 49777ac1-2845-8af5-1789-54cb4a72c850 G�
. G�
.招商银行深圳分行营业部T�-152735.00000000�/��!�;%%


UUO/�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       79035880-87f3-4a3b-ae13-6ab820525793 49777ac1-2845-8af5-1789-54cb4a72c850 G�
. G�
.招商银行深圳分行营业部 XX�-1325136.00000000�0��!�;%%


UUO)�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       5ec52f35-fa80-4795-9f3d-df0dd5006c6a 49777ac1-2845-8af5-1789-54cb4a72c850 G�
. G�
.招商银行深圳分行营业部@�Y�\)-1789.49000000�8��!�;%%


UUO+�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       3465b495-bfa8-41bd-99fe-49e8efb61e26 49777ac1-2845-8af5-1789-54cb4a72c850 G�
. G�
.招商银行深圳分行营业部A(۞z�H�ATz�G�814577.70000000�-��!�;%%


UUO%�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       7604f734-3f8a-4767-a39e-08183d22cb35 49777ac1-2845-8af5-1789-54cb4a72c850 G�
. G�
.招商银行深圳分行营业部@nz�G�240.19000000  ��    H]�6�o��L���
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                �� p/H]�6�o��%�?p�
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     H]�6�o�z<�!�zQSQLite format 3   @      p/  ��  �   -                                                  .n�
{ 	Q �L
v��4��Q�                                                                                                                                                                                                                 �*))�tablebusiness_rulesbusiness_rules ��CREATE TABLE business_rules (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                table_name TEXT NOT NULL,
                rule_category TEXT NOT NULL,
                rule_description TEXT NOT NULL,
                sql_example TEXT,
                importance_level TEXT DEFAULT 'HIGH',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )�(
33�stablecolumn_descriptionscolumn_descriptions ��CREATE TABLE column_descriptions (
                table_name TEXT NOT NULL,
                column_name TEXT NOT NULL,
                chinese_name TEXT,
                description TEXT,
                data_type TEXT,
                business_rules TEXT,
                ai_understanding_points TEXT,
                PRIMARY KEY (table_name, column_name)
            )GY3 indexsqlite_autoindex_column_descriptions_1column_descriptions �Ă?11�)tabletable_descriptionstable_descriptionsCREATE TABLE table_descriptions (
                table_name TEXT PRIMARY KEY,
                description TEXT NOT NULL,
                business_purpose TEXT,
                data_scale TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )EW1 indexsqlite_autoindex_table_descriptions_1table_descriptions ��       �p
KK�[viewfinancial_data_columns_metadatafinancial_data_columns_metadataCREATE VIEW financial_data_columns_metadata AS
            SELECT 
                column_name,
                chinese_name,
                description,
                data_type,
                business_rules,
                ai_understanding_points
            FROM column_descriptions
            WHERE table_name = 'financial_data'
            ORDER BY 
                CASE 
                    WHEN column_name IN ('year', 'month') THEN 1
                    WHEN column_name LIKE 'accounting_%' THEN 2
                    WHEN column_name LIKE 'account_%' THEN 3
                    WHEN column_name LIKE '%amount%' OR column_name LIKE '%cumulative%' OR column_name = 'balance' THEN 4
                    WHEN column_name LIKE 'project_%' THEN 5
                    WHEN column_name LIKE 'bank_%' OR column_name LIKE 'financial_institution_%' THEN 6
                    ELSE 7
                END,
                column_name�S	EE�-viewfinancial_data_with_metadatafinancial_data_with_metadataCREATE VIEW financial_data_with_metadata AS
            SELECT 
                'financial_data' as table_name,
                td.description as table_description,
                td.business_purpose,
                td.data_scale,
                (SELECT COUNT(*) FROM financial_data) as current_record_count
            FROM table_descriptions td
            WHERE td.table_name = 'financial_data'R++Ytablesqlite_sequencesqlite_sequence ��CREATE TABLE sqlite_sequence(name,seq)�]))�qtablefinancial_datafinancial_data �CREATE TABLE "financial_data" ("year" INTEGER, "month" INTEGER, "accounting_organization" INTEGER, "accounting_unit_name" TEXT, "account_code" INTEGER, "account_full_name" TEXT, "account_name" TEXT, "opening_debit_amount" REAL, "opening_credit_amount" INTEGER, "account_direction" TEXT, "project_id" TEXT, "project_code" TEXT, "project_name" TEXT, "market_nature_id" TEXT, "tax_rate_id" TEXT, "tax_rate_name" TEXT, "business_format_id" TEXT, "financial_product_id" TEXT, "long_term_deferred_project_id" TEXT, "property_unit_id" TEXT, "cash_flow_project_id" TEXT, "municipal_enterprise_unit_id" TEXT, "bank_account_id" TEXT, "financial_institution_id" INTEGER, "bank_routing_number" INTEGER, "bank_name" TEXT, "debit_amount" REAL, "debit_cumulative" REAL, "credit_amount" REAL, "credit_cumulative" REAL, "balance" TEXT)       H]�6�o���	b�3̑
   
� 
�                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        �{	)���93financial_data财务辅助科目余额表，包含了企业财务核算的详细信息。
        该表是企业财务管理系统的核心数据表，涵盖了时间维度、组织架构、会计科目、项目管理、银行信息和金额数据等多个方面。用于企业财务核算、报表编制、财务分析和决策支持。
        支持按时间、组织、项目、科目等多维度进行财务数据分析。
        为资产负债表、利润表、现金流量表等财务报表提供基础数据。723,333 行记录，31 个字段，包含整数、实数、文本等多种数据类型2025-07-28 04:19:39  ��    H]�6�o��y� g  ��  P  ��  �  �  �  �  �  �  �  �  �  �  �
  �  �  �
  �	  �  �  �  �  �  �  �  �  �   ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  �   �  �  �  �  �  �  �  �  �	  �
  �  �  �
  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �   �!  �"  �#  �$  �%  �&  �'  �(  �)  �*  �+  �,  �-  �.  �/  �0  �1  �2  �3  �4  �5  �6  �7  �8  �9  �:  �;  �<  �=  �>  �?  �@  �A  �B  �C  �D  �E  �F  �G  �H  �I  �J  �K  �L  �M  �N  �O  �P  �Q  �R  �S  �T  �U  �V  �W  �X  �Y  �Z  �[  �\  �]  �^  �_  �`  �a  �b  �c  �d  �e  �f  �g  �h  �i  �j  �k  �l  �n  �o  �p  �q  �r  �s  �t  �u  �v  �w  �x  �y  �z  �{  �|  �}  �~  �  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  �m  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��   行存款借       79035880-87f3-4a3b-ae13-6ab820525793 49777ac1-2845-8af5-1789-54cb4a72c850 G�
0� G�
0�招商银行股份有限公司深圳分行票据业务部�-4480.00000000�B��!�;%%


UU+�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       f5e52502-a987-41cd-9628-9e72ce7e508f 49777ac1-2845-8af5-1789-54cb4a72c850 G�
0� G�
0�招商银行股份有限公司深圳分行票据业务部p�-28879.00000000�D��!�;%%


UU#�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       7604f734-3f8a-4767-a39e-08183d22cb35 49777ac1-2845-8af5-1789-54cb4a72c850 G�
0� G�
0�招商银行股份有限公司深圳分行票据业务部@Iٙ����51.70000000�I��!�;%%


UU-�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       a4be09b9-c44f-4194-a848-2d1aa0d1911c 49777ac1-2845-8af5-1789-54cb4a72c850 G�
0� G�
0�招商银行股份有限公司深圳分行票据业务部A������-340922.95000000�,��!�;%%


UUO-�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       f5e52502-a987-41cd-9628-9e72ce7e508f 49777ac1-2845-8af5-1789-54cb4a72c850 G�
. G�
.招商银行深圳分行营业部T�-152735.00000000�/��!�;%%


UUO/�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       79035880-87f3-4a3b-ae13-6ab820525793 49777ac1-2845-8af5-1789-54cb4a72c850 G�
. G�
.招商银行深圳分行营业部 XX�-1325136.00000000�0��!�;%%


UUO)�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       5ec52f35-fa80-4795-9f3d-df0dd5006c6a 49777ac1-2845-8af5-1789-54cb4a72c850 G�
. G�
.招商银行深圳分行营业部@�Y�\)-1789.49000000�8��!�;%%


UUO+�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       3465b495-bfa8-41bd-99fe-49e8efb61e26 49777ac1-2845-8af5-1789-54cb4a72c850 G�
. G�
.招商银行深圳分行营业部A(۞z�H�ATz�G�814577.70000000�-��!�;%%


UUO%�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       7604f734-3f8a-4767-a39e-08183d22cb35 49777ac1-2845-8af5-1789-54cb4a72c850 G�
. G�
.招商银行深圳分行营业部@nz�G�240.19000000  ��    H]�6�o�Ẍ́��A��
   � �                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    )	financial_data  ��    H]�6�o��[rԔN�   �   ��
f�	<
l	��@����n�P�J�2 l                                                    �C)G5Uaafinancial_datalong_term_deferred_project_id长期待摊项目ID长期待摊费用项目的标识符TEXT用于长期待摊费用的核算和分摊用于长期待摊费用的核算和分摊�)5)===financial_datafinancial_product_id金融产品ID金融产品的标识符TEXT关联金融产品信息关联金融产品信息�)1=OOfinancial_databusiness_format_id业态ID业务形态的标识符TEXT用于按业务形态分类统计用于按业务形态分类统计�	)'%77efinancial_datatax_rate_name税率名称税率的名称描述TEXT税率的具体名称税率的具体名称，如"13%增值税"等x)#11[financial_datatax_rate_id税率ID税率的标识符TEXT关联税率信息关联税率信息，用于税务分析�)-)=OOfinancial_datamarket_nature_id市场性质ID市场性质的标识符TEXT用于按市场性质分类分析用于按市场性质分类分析�)%%77afinancial_dataproject_name项目名称项目的名称描述TEXT项目的具体名称项目的具体名称，用于报表展示� )%%+7afinancial_dataproject_code项目编号项目的编码TEXT项目的业务编号项目的业务编号，便于项目管理�)!=Oafinancial_dataproject_id项目ID项目的唯一标识符TEXT用于项目维度的财务分析用于项目维度的财务分析和核算�)7ggfinancial_databalance余额科目的期末余额TEXT期末余额，可能包含借贷方向信息期末余额，可能包含借贷方向信息�8)/%aOyfinancial_datacredit_cumulative贷方累计从年初到当期的贷方累计发生额REAL年初至今的贷方累计金额年初至今的贷方累计金额，用于年度分析�7
)-%aOyfinancial_datadebit_cumulative借方累计从年初到当期的借方累计发生额REAL年初至今的借方累计金额年初至今的借方累计金额，用于年度分析�,	)'%C7�financial_datacredit_amount贷方金额当期发生的贷方金额REAL当期贷方发生额当期贷方发生额，反映当期业务活动对科目的贷方影响�+	)%%C7�financial_datadebit_amount借方金额当期发生的借方金额REAL当期借方发生额当期借方发生额，反映当期业务活动对科目的借方影响�C
	)71UC�financial_dataopening_credit_amount期初贷方金额会计期间开始时的贷方余额INTEGER期初余额的贷方部分期初余额的贷方部分，与期初借方金额配合使用�5	)51UCyfinancial_dataopening_debit_amount期初借方金额会计期间开始时的借方余额REAL期初余额的借方部分期初余额的借方部分，用于计算期末余额�M	)/%O;�Gfinancial_dataaccount_direction科目方向会计科目的借贷方向属性TEXT取值: "借" 或 "贷"决定科目的正常余额方向，借方科目增加记借方，贷方科目增加记贷方�J	)%%Cw�financial_dataaccount_name科目名称会计科目的简化名称TEXT通常与account_full_name相同或为其简化版本科目的简称，通常与account_full_name相同或为其简化版本�)/%C7sfinancial_dataaccount_full_name科目全称会计科目的完整名称TEXT科目的详细描述科目的详细描述，用于财务报表和分析�=	)%%C[�financial_dataaccount_code科目编号会计科目的数字编码INTEGER遵循会计准则的科目编码规则会计科目的唯一标识，遵循会计准则的科目编码规则�6	)51CC�financial_dataaccounting_unit_name核算单位名称核算单位的完整名称TEXT核算主体的具体名称核算主体的具体名称，用于报表展示和数据筛选�2);%COyfinancial_dataaccounting_organization核算组织核算组织的代码标识INTEGER用于区分不同的核算主体用于区分不同的核算主体，进行分组统计�	)O1�financial_datamonth月财务数据所属的会计月份INTEGER取值范围: 1-12与year字段配合，精确定位财务数据的时间点�)O=yfinancial_datayear年财务数据所属的会计年度INTEGER用于时间序列分析用于时间序列分析，按年度统计财务�  ��  ��    H]�6�o�s�����
   � ]�8�}
���
pa�=���
{�@��
�
�
��
\
;�                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       )financial_databank_name&)3financial_databank_routing_number+)=financial_datafinancial_institution_id")+financial_databank_account_id/)Efinancial_datamunicipal_enterprise_unit_id')5financial_datacash_flow_project_id#)-financial_dataproperty_unit_id0)Gfinancial_datalong_term_deferred_project_id')5financial_datafinancial_product_id%)1financial_databusiness_format_id )'financial_datatax_rate_name)#financial_datatax_rate_id#)-financial_datamarket_nature_id)%financial_dataproject_name)%financial_dataproject_code)!financial_dataproject_id)financial_databalance$)/financial_datacredit_cumulative#)-financial_datadebit_cumulative
 )'financial_datacredit_amount)%financial_datadebit_amount()7financial_dataopening_credit_amount
')5financial_dataopening_debit_amount	$)/financial_dataaccount_direction)%financial_dataaccount_name$)/financial_dataaccount_full_name)%financial_dataaccount_code')5financial_dataaccounting_unit_name*);financial_dataaccounting_organization)financial_datamonth)	financial_datayear  ��    H]�6�o�ۅZ:�%e
    l j�
f�	<
l	��@����n�P�J�2 l                                                    �C)G5Uaafinancial_datalong_term_deferred_project_id长期待摊项目ID长期待摊费用项目的标识符TEXT用于长期待摊费用的核算和分摊用于长期待摊费用的核算和分摊�)5)===financial_datafinancial_product_id金融产品ID金融产品的标识符TEXT关联金融产品信息关联金融产品信息�)1=OOfinancial_databusiness_format_id业态ID业务形态的标识符TEXT用于按业务形态分类统计用于按业务形态分类统计�	)'%77efinancial_datatax_rate_name税率名称税率的名称描述TEXT税率的具体名称税率的具体名称，如"13%增值税"等x)#11[financial_datatax_rate_id税率ID税率的标识符TEXT关联税率信息关联税率信息，用于税务分析�)-)=OOfinancial_datamarket_nature_id市场性质ID市场性质的标识符TEXT用于按市场性质分类分析用于按市场性质分类分析�)%%77afinancial_dataproject_name项目名称项目的名称描述TEXT项目的具体名称项目的具体名称，用于报表展示� )%%+7afinancial_dataproject_code项目编号项目的编码TEXT项目的业务编号项目的业务编号，便于项目管理�)!=Oafinancial_dataproject_id项目ID项目的唯一标识符TEXT用于项目维度的财务分析用于项目维度的财务分析和核算�)7ggfinancial_databalance余额科目的期末余额TEXT期末余额，可能包含借贷方向信息期末余额，可能包含借贷方向信息�8)/%aOyfinancial_datacredit_cumulative贷方累计从年初到当期的贷方累计发生额REAL年初至今的贷方累计金额年初至今的贷方累计金额，用于年度分析�7
)-%aOyfinancial_datadebit_cumulative借方累计从年初到当期的借方累计发生额REAL年初至今的借方累计金额年初至今的借方累计金额，用于年度分析�,	)'%C7�financial_datacredit_amount贷方金额当期发生的贷方金额REAL当期贷方发生额当期贷方发生额，反映当期业务活动对科目的贷方影响�+	)%%C7�financial_datadebit_amount借方金额当期发生的借方金额REAL当期借方发生额当期借方发生额，反映当期业务活动对科目的借方影响�C
	)71UC�financial_dataopening_credit_amount期初贷方金额会计期间开始时的贷方余额INTEGER期初余额的贷方部分期初余额的贷方部分，与期初借方金额配合使用�5	)51UCyfinancial_dataopening_debit_amount期初借方金额会计期间开始时的借方余额REAL期初余额的借方部分期初余额的借方部分，用于计算期末余额�M	)/%O;�Gfinancial_dataaccount_direction科目方向会计科目的借贷方向属性TEXT取值: "借" 或 "贷"决定科目的正常余额方向，借方科目增加记借方，贷方科目增加记贷方�J	)%%Cw�financial_dataaccount_name科目名称会计科目的简化名称TEXT通常与account_full_name相同或为其简化版本科目的简称，通常与account_full_name相同或为其简化版本�)/%C7sfinancial_dataaccount_full_name科目全称会计科目的完整名称TEXT科目的详细描述科目的详细描述，用于财务报表和分析�=	)%%C[�financial_dataaccount_code科目编号会计科目的数字编码INTEGER遵循会计准则的科目编码规则会计科目的唯一标识，遵循会计准则的科目编码规则�6	)51CC�financial_dataaccounting_unit_name核算单位名称核算单位的完整名称TEXT核算主体的具体名称核算主体的具体名称，用于报表展示和数据筛选�2);%COyfinancial_dataaccounting_organization核算组织核算组织的代码标识INTEGER用于区分不同的核算主体用于区分不同的核算主体，进行分组统计�	)O1�financial_datamonth月财务数据所属的会计月份INTEGER取值范围: 1-12与year字段配合，精确定位财务数据的时间点�)O=yfinancial_datayear年财务数据所属的会计年度INTEGER用于时间序列分析用于时间序列分析，按年度统计财务数据  ��    H]�6�o��2{(��]�
   ~ 9
7z~                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            �y
 )1�Y�13financial_data科目编号识别1xxx=资产类，2xxx=负债类，3xxx=所有者权益类，60xx=收入类，64xx/66xx=成本费用类SELECT account_code, account_name FROM financial_data WHERE account_code LIKE "1%"HIGH2025-07-28 04:19:39�:	 )1�!m3financial_data数据类型转换balance字段为TEXT类型，需要使用CAST(balance AS REAL)进行转换SELECT CAST(balance AS REAL) FROM financial_dataHIGH2025-07-28 04:19:39�
 )O��W3financial_data科目分类与金额字段对应成本费用类科目必须使用debit_amount或debit_cumulative字段SELECT SUM(debit_amount) FROM financial_data WHERE account_code LIKE "64%" OR account_code LIKE "66%"CRITICAL2025-07-28 04:19:39�a
 )O��#3financial_data科目分类与金额字段对应收入类科目必须使用credit_amount或credit_cumulative字段SELECT SUM(credit_amount) FROM financial_data WHERE account_code LIKE "60%"CRITICAL2025-07-28 04:19:39�`
 )O��13financial_data科目分类与金额字段对应资产负债类科目必须使用balance字段进行汇总SELECT SUM(CAST(balance AS REAL)) FROM financial_data WHERE account_code LIKE "1%"CRITICAL2025-07-28 04:19:39  ��    H]�6�o�r���	�m#
   � �                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  )business_rules  � p/H]�6�o���
?�D�
   � A�
�
@��                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    �)%+7kfinancial_databank_name银行名称银行的名称TEXT具体的银行名称具体的银行名称，如"中国工商银行"�
)37IIfinancial_databank_routing_number联行号银行的联行号码INTEGER银行间清算的标识号码银行间清算的标识号码�)=)=CCfinancial_datafinancial_institution_id金融机构ID金融机构的标识符INTEGER标识具体的金融机构标识具体的金融机构�)+)=OOfinancial_databank_account_id银行账号ID银行账户的标识符TEXT关联具体的银行账户信息关联具体的银行账户信息�6)E5UUUfinancial_datamunicipal_enterprise_unit_id市属国企单位ID市属国有企业单位的标识符TEXT特定于国有企业的分类标识特定于国有企业的分类标识�1)55O[[financial_datacash_flow_project_id现金流量项目ID现金流量表项目的标识符TEXT用于现金流量表的编制和分析用于现金流量表的编制和分析�<)-)aggfinancial_dataproperty_unit_id楼盘房号ID房地产项目中具体房号的标识符TEXT房地产业务中的具体房产单位标识房地产业务中的具体房产单位标识    p/H]�6�o�UchDtDkSQLite format 3   @      p/  ��  �   .                                                  .n�
   	9 �L
v��<9�                                                                                                                                                                                           �H
33�3tablecolumn_descriptionscolumn_descriptions ��CREATE TABLE column_descriptions (
                table_name TEXT NOT NULL,
                column_name TEXT NOT NULL,
                chinese_name TEXT,
                description TEXT,
                data_type TEXT,
                business_rules TEXT,
                ai_understanding_points TEXT, field_category TEXT DEFAULT '',
                PRIMARY KEY (table_name, column_name)
            )�*))�tablebusiness_rulesbusiness_rules ��CREATE TABLE business_rules (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                table_name TEXT NOT NULL,
                rule_category TEXT NOT NULL,
                rule_description TEXT NOT NULL,
                sql_example TEXT,
                importance_level TEXT DEFAULT 'HIGH',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )GY3 indexsqlite_autoindex_column_descriptions_1column_descriptions �Ă?11�)tabletable_descriptionstable_descriptionsCREATE TABLE table_descriptions (
                table_name TEXT PRIMARY KEY,
                description TEXT NOT NULL,
                business_purpose TEXT,
                data_scale TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )EW1 indexsqlite_autoindex_table_descriptions_1table_descriptions �p
KK�[viewfinancial_data_columns_metadatafinancial_data_columns_metadataCREATE VIEW financial_data_columns_metadata AS
            SELECT 
                column_name,
                chinese_name,
                description,
                data_type,
                business_rules,
                ai_understanding_points
            FROM column_descriptions
            WHERE table_name = 'financial_data'
            ORDER BY 
                CASE 
                    WHEN column_name IN ('year', 'month') THEN 1
                    WHEN column_name LIKE 'accounting_%' THEN 2
                    WHEN column_name LIKE 'account_%' THEN 3
                    WHEN column_name LIKE '%amount%' OR column_name LIKE '%cumulative%' OR column_name = 'balance' THEN 4
                    WHEN column_name LIKE 'project_%' THEN 5
                    WHEN column_name LIKE 'bank_%' OR column_name LIKE 'financial_institution_%' THEN 6
                    ELSE 7
                END,
                column_name�S	EE�-viewfinancial_data_with_metadatafinancial_data_with_metadataCREATE VIEW financial_data_with_metadata AS
            SELECT 
                'financial_data' as table_name,
                td.description as table_description,
                td.business_purpose,
                td.data_scale,
                (SELECT COUNT(*) FROM financial_data) as current_record_count
            FROM table_descriptions td
            WHERE td.table_name = 'financial_data'R++Ytablesqlite_sequencesqlite_sequence ��CREATE TABLE sqlite_sequence(name,seq)�]))�qtablefinancial_datafinancial_data �CREATE TABLE "financial_data" ("year" INTEGER, "month" INTEGER, "accounting_organization" INTEGER, "accounting_unit_name" TEXT, "account_code" INTEGER, "account_full_name" TEXT, "account_name" TEXT, "opening_debit_amount" REAL, "opening_credit_amount" INTEGER, "account_direction" TEXT, "project_id" TEXT, "project_code" TEXT, "project_name" TEXT, "market_nature_id" TEXT, "tax_rate_id" TEXT, "tax_rate_name" TEXT, "business_format_id" TEXT, "financial_product_id" TEXT, "long_term_deferred_project_id" TEXT, "property_unit_id" TEXT, "cash_flow_project_id" TEXT, "municipal_enterprise_unit_id" TEXT, "bank_account_id" TEXT, "financial_institution_id" INTEGER, "bank_routing_number" INTEGER, "bank_name" TEXT, "debit_amount" REAL, "debit_cumulative" REAL, "credit_amount" REAL, "credit_cumulative" REAL, "balance" TEXT)    p/H]�6�o����ە�@SQLite format 3   @      p/  ��  �   /                                                  .n�
   	 �L
v��<�                                                                                                                                                          �i
33�utablecolumn_descriptionscolumn_descriptions ��CREATE TABLE column_descriptions (
                table_name TEXT NOT NULL,
                column_name TEXT NOT NULL,
                chinese_name TEXT,
                description TEXT,
                data_type TEXT,
                business_rules TEXT,
                ai_understanding_points TEXT, field_category TEXT DEFAULT '', usage_scenarios TEXT DEFAULT '',
                PRIMARY KEY (table_name, column_name)
            )�*))�tablebusiness_rulesbusiness_rules ��CREATE TABLE business_rules (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                table_name TEXT NOT NULL,
                rule_category TEXT NOT NULL,
                rule_description TEXT NOT NULL,
                sql_example TEXT,
                importance_level TEXT DEFAULT 'HIGH',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )GY3 indexsqlite_autoindex_column_descriptions_1column_descriptions �Ă?11�)tabletable_descriptionstable_descriptionsCREATE TABLE table_descriptions (
                table_name TEXT PRIMARY KEY,
                description TEXT NOT NULL,
                business_purpose TEXT,
                data_scale TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )EW1 indexsqlite_autoindex_table_descriptions_1table_descriptions �p
KK�[viewfinancial_data_columns_metadatafinancial_data_columns_metadataCREATE VIEW financial_data_columns_metadata AS
            SELECT 
                column_name,
                chinese_name,
                description,
                data_type,
                business_rules,
                ai_understanding_points
            FROM column_descriptions
            WHERE table_name = 'financial_data'
            ORDER BY 
                CASE 
                    WHEN column_name IN ('year', 'month') THEN 1
                    WHEN column_name LIKE 'accounting_%' THEN 2
                    WHEN column_name LIKE 'account_%' THEN 3
                    WHEN column_name LIKE '%amount%' OR column_name LIKE '%cumulative%' OR column_name = 'balance' THEN 4
                    WHEN column_name LIKE 'project_%' THEN 5
                    WHEN column_name LIKE 'bank_%' OR column_name LIKE 'financial_institution_%' THEN 6
                    ELSE 7
                END,
                column_name�S	EE�-viewfinancial_data_with_metadatafinancial_data_with_metadataCREATE VIEW financial_data_with_metadata AS
            SELECT 
                'financial_data' as table_name,
                td.description as table_description,
                td.business_purpose,
                td.data_scale,
                (SELECT COUNT(*) FROM financial_data) as current_record_count
            FROM table_descriptions td
            WHERE td.table_name = 'financial_data'R++Ytablesqlite_sequencesqlite_sequence ��CREATE TABLE sqlite_sequence(name,seq)�]))�qtablefinancial_datafinancial_data �CREATE TABLE "financial_data" ("year" INTEGER, "month" INTEGER, "accounting_organization" INTEGER, "accounting_unit_name" TEXT, "account_code" INTEGER, "account_full_name" TEXT, "account_name" TEXT, "opening_debit_amount" REAL, "opening_credit_amount" INTEGER, "account_direction" TEXT, "project_id" TEXT, "project_code" TEXT, "project_name" TEXT, "market_nature_id" TEXT, "tax_rate_id" TEXT, "tax_rate_name" TEXT, "business_format_id" TEXT, "financial_product_id" TEXT, "long_term_deferred_project_id" TEXT, "property_unit_id" TEXT, "cash_flow_project_id" TEXT, "municipal_enterprise_unit_id" TEXT, "bank_account_id" TEXT, "financial_institution_id" INTEGER, "bank_routing_number" INTEGER, "bank_name" TEXT, "debit_amount" REAL, "debit_cumulative" REAL, "credit_amount" REAL, "credit_cumulative" REAL, "balance" TEXT)    p/H]�6�o���_#
SQLite format 3   @      p/  ��  �   0                                                  .n�
   	 � �L
v��< ��                                                                                                                           �
33�3tablecolumn_descriptionscolumn_descriptions ��CREATE TABLE column_descriptions (
                table_name TEXT NOT NULL,
                column_name TEXT NOT NULL,
                chinese_name TEXT,
                description TEXT,
                data_type TEXT,
                business_rules TEXT,
                ai_understanding_points TEXT, field_category TEXT DEFAULT '', usage_scenarios TEXT DEFAULT '', common_values TEXT DEFAULT '',
                PRIMARY KEY (table_name, column_name)
            )�*))�tablebusiness_rulesbusiness_rules ��CREATE TABLE business_rules (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                table_name TEXT NOT NULL,
                rule_category TEXT NOT NULL,
                rule_description TEXT NOT NULL,
                sql_example TEXT,
                importance_level TEXT DEFAULT 'HIGH',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )GY3 indexsqlite_autoindex_column_descriptions_1column_descriptions �Ă?11�)tabletable_descriptionstable_descriptionsCREATE TABLE table_descriptions (
                table_name TEXT PRIMARY KEY,
                description TEXT NOT NULL,
                business_purpose TEXT,
                data_scale TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )EW1 indexsqlite_autoindex_table_descriptions_1table_descriptions �p
KK�[viewfinancial_data_columns_metadatafinancial_data_columns_metadataCREATE VIEW financial_data_columns_metadata AS
            SELECT 
                column_name,
                chinese_name,
                description,
                data_type,
                business_rules,
                ai_understanding_points
            FROM column_descriptions
            WHERE table_name = 'financial_data'
            ORDER BY 
                CASE 
                    WHEN column_name IN ('year', 'month') THEN 1
                    WHEN column_name LIKE 'accounting_%' THEN 2
                    WHEN column_name LIKE 'account_%' THEN 3
                    WHEN column_name LIKE '%amount%' OR column_name LIKE '%cumulative%' OR column_name = 'balance' THEN 4
                    WHEN column_name LIKE 'project_%' THEN 5
                    WHEN column_name LIKE 'bank_%' OR column_name LIKE 'financial_institution_%' THEN 6
                    ELSE 7
                END,
                column_name�S	EE�-viewfinancial_data_with_metadatafinancial_data_with_metadataCREATE VIEW financial_data_with_metadata AS
            SELECT 
                'financial_data' as table_name,
                td.description as table_description,
                td.business_purpose,
                td.data_scale,
                (SELECT COUNT(*) FROM financial_data) as current_record_count
            FROM table_descriptions td
            WHERE td.table_name = 'financial_data'R++Ytablesqlite_sequencesqlite_sequence ��CREATE TABLE sqlite_sequence(name,seq)�]))�qtablefinancial_datafinancial_data �CREATE TABLE "financial_data" ("year" INTEGER, "month" INTEGER, "accounting_organization" INTEGER, "accounting_unit_name" TEXT, "account_code" INTEGER, "account_full_name" TEXT, "account_name" TEXT, "opening_debit_amount" REAL, "opening_credit_amount" INTEGER, "account_direction" TEXT, "project_id" TEXT, "project_code" TEXT, "project_name" TEXT, "market_nature_id" TEXT, "tax_rate_id" TEXT, "tax_rate_name" TEXT, "business_format_id" TEXT, "financial_product_id" TEXT, "long_term_deferred_project_id" TEXT, "property_unit_id" TEXT, "cash_flow_project_id" TEXT, "municipal_enterprise_unit_id" TEXT, "bank_account_id" TEXT, "financial_institution_id" INTEGER, "bank_routing_number" INTEGER, "bank_name" TEXT, "debit_amount" REAL, "debit_cumulative" REAL, "credit_amount" REAL, "credit_cumulative" REAL, "balance" TEXT)    p/H]�6�o�kC�~�ISQLite format 3   @      p/  ��  �   1                                                  .n�
   	 � �L
v��< ��                                                                                           �(
33�stablecolumn_descriptionscolumn_descriptions ��CREATE TABLE column_descriptions (
                table_name TEXT NOT NULL,
                column_name TEXT NOT NULL,
                chinese_name TEXT,
                description TEXT,
                data_type TEXT,
                business_rules TEXT,
                ai_understanding_points TEXT, field_category TEXT DEFAULT '', usage_scenarios TEXT DEFAULT '', common_values TEXT DEFAULT '', related_fields TEXT DEFAULT '',
                PRIMARY KEY (table_name, column_name)
            )�*))�tablebusiness_rulesbusiness_rules ��CREATE TABLE business_rules (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                table_name TEXT NOT NULL,
                rule_category TEXT NOT NULL,
                rule_description TEXT NOT NULL,
                sql_example TEXT,
                importance_level TEXT DEFAULT 'HIGH',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )GY3 indexsqlite_autoindex_column_descriptions_1column_descriptions �Ă?11�)tabletable_descriptionstable_descriptionsCREATE TABLE table_descriptions (
                table_name TEXT PRIMARY KEY,
                description TEXT NOT NULL,
                business_purpose TEXT,
                data_scale TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )EW1 indexsqlite_autoindex_table_descriptions_1table_descriptions �p
KK�[viewfinancial_data_columns_metadatafinancial_data_columns_metadataCREATE VIEW financial_data_columns_metadata AS
            SELECT 
                column_name,
                chinese_name,
                description,
                data_type,
                business_rules,
                ai_understanding_points
            FROM column_descriptions
            WHERE table_name = 'financial_data'
            ORDER BY 
                CASE 
                    WHEN column_name IN ('year', 'month') THEN 1
                    WHEN column_name LIKE 'accounting_%' THEN 2
                    WHEN column_name LIKE 'account_%' THEN 3
                    WHEN column_name LIKE '%amount%' OR column_name LIKE '%cumulative%' OR column_name = 'balance' THEN 4
                    WHEN column_name LIKE 'project_%' THEN 5
                    WHEN column_name LIKE 'bank_%' OR column_name LIKE 'financial_institution_%' THEN 6
                    ELSE 7
                END,
                column_name�S	EE�-viewfinancial_data_with_metadatafinancial_data_with_metadataCREATE VIEW financial_data_with_metadata AS
            SELECT 
                'financial_data' as table_name,
                td.description as table_description,
                td.business_purpose,
                td.data_scale,
                (SELECT COUNT(*) FROM financial_data) as current_record_count
            FROM table_descriptions td
            WHERE td.table_name = 'financial_data'R++Ytablesqlite_sequencesqlite_sequence ��CREATE TABLE sqlite_sequence(name,seq)�]))�qtablefinancial_datafinancial_data �CREATE TABLE "financial_data" ("year" INTEGER, "month" INTEGER, "accounting_organization" INTEGER, "accounting_unit_name" TEXT, "account_code" INTEGER, "account_full_name" TEXT, "account_name" TEXT, "opening_debit_amount" REAL, "opening_credit_amount" INTEGER, "account_direction" TEXT, "project_id" TEXT, "project_code" TEXT, "project_name" TEXT, "market_nature_id" TEXT, "tax_rate_id" TEXT, "tax_rate_name" TEXT, "business_format_id" TEXT, "financial_product_id" TEXT, "long_term_deferred_project_id" TEXT, "property_unit_id" TEXT, "cash_flow_project_id" TEXT, "municipal_enterprise_unit_id" TEXT, "bank_account_id" TEXT, "financial_institution_id" INTEGER, "bank_routing_number" INTEGER, "bank_name" TEXT, "debit_amount" REAL, "debit_cumulative" REAL, "credit_amount" REAL, "credit_cumulative" REAL, "balance" TEXT)    p/H]�6�o���IΪ�SQLite format 3   @      p/  ��  �   2                                                  .n�
   	 � �L
v��< ��                                                        �K
33�9tablecolumn_descriptionscolumn_descriptions ��CREATE TABLE column_descriptions (
                table_name TEXT NOT NULL,
                column_name TEXT NOT NULL,
                chinese_name TEXT,
                description TEXT,
                data_type TEXT,
                business_rules TEXT,
                ai_understanding_points TEXT, field_category TEXT DEFAULT '', usage_scenarios TEXT DEFAULT '', common_values TEXT DEFAULT '', related_fields TEXT DEFAULT '', calculation_rules TEXT DEFAULT '',
                PRIMARY KEY (table_name, column_name)
            )�*))�tablebusiness_rulesbusiness_rules ��CREATE TABLE business_rules (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                table_name TEXT NOT NULL,
                rule_category TEXT NOT NULL,
                rule_description TEXT NOT NULL,
                sql_example TEXT,
                importance_level TEXT DEFAULT 'HIGH',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )GY3 indexsqlite_autoindex_column_descriptions_1column_descriptions �Ă?11�)tabletable_descriptionstable_descriptionsCREATE TABLE table_descriptions (
                table_name TEXT PRIMARY KEY,
                description TEXT NOT NULL,
                business_purpose TEXT,
                data_scale TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )EW1 indexsqlite_autoindex_table_descriptions_1table_descriptions �p
KK�[viewfinancial_data_columns_metadatafinancial_data_columns_metadataCREATE VIEW financial_data_columns_metadata AS
            SELECT 
                column_name,
                chinese_name,
                description,
                data_type,
                business_rules,
                ai_understanding_points
            FROM column_descriptions
            WHERE table_name = 'financial_data'
            ORDER BY 
                CASE 
                    WHEN column_name IN ('year', 'month') THEN 1
                    WHEN column_name LIKE 'accounting_%' THEN 2
                    WHEN column_name LIKE 'account_%' THEN 3
                    WHEN column_name LIKE '%amount%' OR column_name LIKE '%cumulative%' OR column_name = 'balance' THEN 4
                    WHEN column_name LIKE 'project_%' THEN 5
                    WHEN column_name LIKE 'bank_%' OR column_name LIKE 'financial_institution_%' THEN 6
                    ELSE 7
                END,
                column_name�S	EE�-viewfinancial_data_with_metadatafinancial_data_with_metadataCREATE VIEW financial_data_with_metadata AS
            SELECT 
                'financial_data' as table_name,
                td.description as table_description,
                td.business_purpose,
                td.data_scale,
                (SELECT COUNT(*) FROM financial_data) as current_record_count
            FROM table_descriptions td
            WHERE td.table_name = 'financial_data'R++Ytablesqlite_sequencesqlite_sequence ��CREATE TABLE sqlite_sequence(name,seq)�]))�qtablefinancial_datafinancial_data �CREATE TABLE "financial_data" ("year" INTEGER, "month" INTEGER, "accounting_organization" INTEGER, "accounting_unit_name" TEXT, "account_code" INTEGER, "account_full_name" TEXT, "account_name" TEXT, "opening_debit_amount" REAL, "opening_credit_amount" INTEGER, "account_direction" TEXT, "project_id" TEXT, "project_code" TEXT, "project_name" TEXT, "market_nature_id" TEXT, "tax_rate_id" TEXT, "tax_rate_name" TEXT, "business_format_id" TEXT, "financial_product_id" TEXT, "long_term_deferred_project_id" TEXT, "property_unit_id" TEXT, "cash_flow_project_id" TEXT, "municipal_enterprise_unit_id" TEXT, "bank_account_id" TEXT, "financial_institution_id" INTEGER, "bank_routing_number" INTEGER, "bank_name" TEXT, "debit_amount" REAL, "debit_cumulative" REAL, "credit_amount" REAL, "credit_cumulative" REAL, "balance" TEXT)    p/H]�6�o�BҌ 3ϻ�SQLite format 3   @      p/  ��  �   3                                                  .n�
   	 � �L
v��< ��                       �l
33�{tablecolumn_descriptionscolumn_descriptions ��CREATE TABLE column_descriptions (
                table_name TEXT NOT NULL,
                column_name TEXT NOT NULL,
                chinese_name TEXT,
                description TEXT,
                data_type TEXT,
                business_rules TEXT,
                ai_understanding_points TEXT, field_category TEXT DEFAULT '', usage_scenarios TEXT DEFAULT '', common_values TEXT DEFAULT '', related_fields TEXT DEFAULT '', calculation_rules TEXT DEFAULT '', ai_prompt_hints TEXT DEFAULT '',
                PRIMARY KEY (table_name, column_name)
            )�*))�tablebusiness_rulesbusiness_rules ��CREATE TABLE business_rules (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                table_name TEXT NOT NULL,
                rule_category TEXT NOT NULL,
                rule_description TEXT NOT NULL,
                sql_example TEXT,
                importance_level TEXT DEFAULT 'HIGH',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )GY3 indexsqlite_autoindex_column_descriptions_1column_descriptions �Ă?11�)tabletable_descriptionstable_descriptionsCREATE TABLE table_descriptions (
                table_name TEXT PRIMARY KEY,
                description TEXT NOT NULL,
                business_purpose TEXT,
                data_scale TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )EW1 indexsqlite_autoindex_table_descriptions_1table_descriptions �p
KK�[viewfinancial_data_columns_metadatafinancial_data_columns_metadataCREATE VIEW financial_data_columns_metadata AS
            SELECT 
                column_name,
                chinese_name,
                description,
                data_type,
                business_rules,
                ai_understanding_points
            FROM column_descriptions
            WHERE table_name = 'financial_data'
            ORDER BY 
                CASE 
                    WHEN column_name IN ('year', 'month') THEN 1
                    WHEN column_name LIKE 'accounting_%' THEN 2
                    WHEN column_name LIKE 'account_%' THEN 3
                    WHEN column_name LIKE '%amount%' OR column_name LIKE '%cumulative%' OR column_name = 'balance' THEN 4
                    WHEN column_name LIKE 'project_%' THEN 5
                    WHEN column_name LIKE 'bank_%' OR column_name LIKE 'financial_institution_%' THEN 6
                    ELSE 7
                END,
                column_name�S	EE�-viewfinancial_data_with_metadatafinancial_data_with_metadataCREATE VIEW financial_data_with_metadata AS
            SELECT 
                'financial_data' as table_name,
                td.description as table_description,
                td.business_purpose,
                td.data_scale,
                (SELECT COUNT(*) FROM financial_data) as current_record_count
            FROM table_descriptions td
            WHERE td.table_name = 'financial_data'R++Ytablesqlite_sequencesqlite_sequence ��CREATE TABLE sqlite_sequence(name,seq)�]))�qtablefinancial_datafinancial_data �CREATE TABLE "financial_data" ("year" INTEGER, "month" INTEGER, "accounting_organization" INTEGER, "accounting_unit_name" TEXT, "account_code" INTEGER, "account_full_name" TEXT, "account_name" TEXT, "opening_debit_amount" REAL, "opening_credit_amount" INTEGER, "account_direction" TEXT, "project_id" TEXT, "project_code" TEXT, "project_name" TEXT, "market_nature_id" TEXT, "tax_rate_id" TEXT, "tax_rate_name" TEXT, "business_format_id" TEXT, "financial_product_id" TEXT, "long_term_deferred_project_id" TEXT, "property_unit_id" TEXT, "cash_flow_project_id" TEXT, "municipal_enterprise_unit_id" TEXT, "bank_account_id" TEXT, "financial_institution_id" INTEGER, "bank_routing_number" INTEGER, "bank_name" TEXT, "debit_amount" REAL, "debit_cumulative" REAL, "credit_amount" REAL, "credit_cumulative" REAL, "balance" TEXT)       H]�6�o�?���ɪpeSQLite format 3   @      p/  ��  �   4                                                  .n�   �   �����< �� �                  �l
33�{tablecolumn_descriptionscolumn_descriptions ��CREATE TABLE column_descriptions (
                table_name TEXT NOT NULL,
                column_name TEXT NOT NULL,
                chinese_name TEXT,
                description TEXT,
                data_type TEXT,
                business_rules TEXT,
                ai_understanding_points TEXT, field_category TEXT DEFAULT '', usage_scenarios TEXT DEFAULT '', common_values TEXT DEFAULT '', related_fields TEXT DEFAULT '', calculation_rules TEXT DEFAULT '', ai_prompt_hints TEXT DEFAULT '',
                PRIMARY KEY (table_name, column_name)
            )�*))�tablebusiness_rulesbusiness_rules ��CREATE TABLE business_rules (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                table_name TEXT NOT NULL,
                rule_category TEXT NOT NULL,
                rule_description TEXT NOT NULL,
                sql_example TEXT,
                importance_level TEXT DEFAULT 'HIGH',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )GY3 indexsqlite_autoindex_column_descriptions_1column_descriptions �Ă?11�)tabletable_descriptionstable_descriptionsCREATE TABLE table_descriptions (
                table_name TEXT PRIMARY KEY,
                description TEXT NOT NULL,
                business_purpose TEXT,
                data_scale TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )EW1 indexsqlite_autoindex_table_descriptions_1table_descriptions �p
KK�[viewfinancial_data_columns_metadatafinancial_data_columns_metadataCREATE VIEW financial_data_columns_metadata AS
            SELECT 
                column_name,
                chinese_name,
                description,
                data_type,
                business_rules,
                ai_understanding_points
            FROM column_descriptions
            WHERE table_name = 'financial_data'
            ORDER BY 
                CASE 
                    WHEN column_name IN ('year', 'month') THEN 1
                    WHEN column_name LIKE 'accounting_%' THEN 2
                    WHEN column_name LIKE 'account_%' THEN 3
                    WHEN column_name LIKE '%amount%' OR column_name LIKE '%cumulative%' OR column_name = 'balance' THEN 4
                    WHEN column_name LIKE 'project_%' THEN 5
                    WHEN column_name LIKE 'bank_%' OR column_name LIKE 'financial_institution_%' THEN 6
                    ELSE 7
                END,
                column_name�S	EE�-viewfinancial_data_with_metadatafinancial_data_with_metadataCREATE VIEW financial_data_with_metadata AS
            SELECT 
                'financial_data' as table_name,
                td.description as table_description,
                td.business_purpose,
                td.data_scale,
                (SELECT COUNT(*) FROM financial_data) as current_record_count
            FROM table_descriptions td
            WHERE td.table_name = 'financial_data'R++Ytablesqlite_sequencesqlite_sequence ��CREATE TABLE sqlite_sequence(name,seq)�]))�qtablefinancial_datafinancial_data �CREATE TABLE "financial_data" ("year" INTEGER, "month" INTEGER, "accounting_organization" INTEGER, "accounting_unit_name" TEXT, "account_code" INTEGER, "account_full_name" TEXT, "account_name" TEXT, "opening_debit_amount" REAL, "opening_credit_amount" INTEGER, "account_direction" TEXT, "project_id" TEXT, "project_code" TEXT, "project_name" TEXT, "market_nature_id" TEXT, "tax_rate_id" TEXT, "tax_rate_name" TEXT, "business_format_id" TEXT, "financial_product_id" TEXT, "long_term_deferred_project_id" TEXT, "property_unit_id" TEXT, "cash_flow_project_id" TEXT, "municipal_enterprise_unit_id" TEXT, "bank_account_id" TEXT, "financial_institution_id" INTEGER, "bank_routing_number" INTEGER, "bank_name" TEXT, "debit_amount" REAL, "debit_cumulative" REAL, "credit_amount" REAL, "credit_cumulative" REAL, "balance"   ��
  ��    H]�6�o�Ϭ���}�[  ��  M  ��  �  �  �  �  �  �  �  �  �  �  �
  �  �  �
  �	  �  �  �  �  �  �  �  �  �   ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  �   �  �  �  �  �  �  �  �  �	  �
  �  �  �
  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �   �!  �"  �#  �$  �%  �&  �'  �(  �)  �*  �+  �,  �-  �.  �/  �0  �1  �2  �3  �4  �5  �6  �7  �8  �9  �:  �;  �<  �=  �>  �?  �@  �A  �B  �C  �D  �E  �F  �G  �H  �I  �J  �K  �L  �M  �N  �O  �P  �Q  �R  �S  �T  �U  �V  �W  �X  �Y  �Z  �[  �\  �]  �^  �_  �`  �a  �b  �c  �d  �e  �f  �g  �h  �i  �j  �k  �l  �n  �o  �p  �q  �r  �s  �t  �u  �v  �w  �x  �y  �z  �{  �|  �}  �~  �  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  �m  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��   行存款借       79035880-87f3-4a3b-ae13-6ab820525793 49777ac1-2845-8af5-1789-54cb4a72c850 G�
0� G�
0�招商银行股份有限公司深圳分行票据业务部�-4480.00000000�B��!�;%%


UU+�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       f5e52502-a987-41cd-9628-9e72ce7e508f 49777ac1-2845-8af5-1789-54cb4a72c850 G�
0� G�
0�招商银行股份有限公司深圳分行票据业务部p�-28879.00000000�D��!�;%%


UU#�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       7604f734-3f8a-4767-a39e-08183d22cb35 49777ac1-2845-8af5-1789-54cb4a72c850 G�
0� G�
0�招商银行股份有限公司深圳分行票据业务部@Iٙ����51.70000000�I��!�;%%


UU-�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       a4be09b9-c44f-4194-a848-2d1aa0d1911c 49777ac1-2845-8af5-1789-54cb4a72c850 G�
0� G�
0�招商银行股份有限公司深圳分行票据业务部A������-340922.95000000�,��!�;%%


UUO-�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       f5e52502-a987-41cd-9628-9e72ce7e508f 49777ac1-2845-8af5-1789-54cb4a72c850 G�
. G�
.招商银行深圳分行营业部T�-152735.00000000�/��!�;%%


UUO/�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       79035880-87f3-4a3b-ae13-6ab820525793 49777ac1-2845-8af5-1789-54cb4a72c850 G�
. G�
.招商银行深圳分行营业部 XX�-1325136.00000000�0��!�;%%


UUO)�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       5ec52f35-fa80-4795-9f3d-df0dd5006c6a 49777ac1-2845-8af5-1789-54cb4a72c850 G�
. G�
.招商银行深圳分行营业部@�Y�\)-1789.49000000�8��!�;%%


UUO+�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       3465b495-bfa8-41bd-99fe-49e8efb61e26 49777ac1-2845-8af5-1789-54cb4a72c850 G�
. G�
.招商银行深圳分行营业部A(۞z�H�ATz�G�814577.70000000�-��!�;%%


UUO%�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       7604f734-3f8a-4767-a39e-08183d22cb35 49777ac1-2845-8af5-1789-54cb4a72c850 G�
. G�
.招商银行深圳分行营业部@nz�G�240.19000000  ��    H]�6�o�r�tf�ev
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                ��    H]�6�o��3�~�IK
   � �L
v��< �� �                  �l
33�{tablecolumn_descriptionscolumn_descriptions ��CREATE TABLE column_d                       �l
33�{tablecolumn_descriptionscolumn_descriptions ��CREATE TABLE column_descriptions (
                table_name TEXT NOT NULL,
                column_name TEXT NOT NULL,
                chinese_name TEXT,
                description TEXT,
                data_type TEXT,
                business_rules TEXT,
                ai_understanding_points TEXT, field_category TEXT DEFAULT '', usage_scenarios TEXT DEFAULT '', common_values TEXT DEFAULT '', related_fields TEXT DEFAULT '', calculation_rules TEXT DEFAULT '', ai_prompt_hints TEXT DEFAULT '',
                PRIMARY KEY (table_name, column_name)
            )  ))�tablebusiness_rulesbusiness_rules ��CREATE TABLE business_rules (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                table_name TEXT NOT NULL,
                rule_category TEXT NOT NULL,
                rule_description TEXT NOT NULL,
                sql_example TEXT,
                importance_level TEXT DEFAULT 'HIGH',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )GY3 indexsqlite_autoindex_column_descriptions_1column_descriptions �Ă?11�)tabletable_descriptionstable_descriptionsCREATE TABLE table_descriptions (
                table_name TEXT PRIMARY KEY,
                description TEXT NOT NULL,
                business_purpose TEXT,
                data_scale TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )EW1 indexsqlite_autoindex_table_descriptions_1table_descriptions �p
KK�[viewfinancial_data_columns_metadatafinancial_data_columns_metadataCREATE VIEW financial_data_columns_metadata AS
            SELECT 
                column_name,
                chinese_name,
                description,
                data_type,
                business_rules,
                ai_understanding_points
            FROM column_descriptions
            WHERE table_name = 'financial_data'
            ORDER BY 
                CASE 
                    WHEN column_name IN ('year', 'month') THEN 1
                    WHEN column_name LIKE 'accounting_%' THEN 2
                    WHEN column_name LIKE 'account_%' THEN 3
                    WHEN column_name LIKE '%amount%' OR column_name LIKE '%cumulative%' OR column_name = 'balance' THEN 4
                    WHEN column_name LIKE 'project_%' THEN 5
                    WHEN column_name LIKE 'bank_%' OR column_name LIKE 'financial_institution_%' THEN 6
                    ELSE 7
                END,
                column_name�S	EE�-viewfinancial_data_with_metadatafinancial_data_with_metadataCREATE VIEW financial_data_with_metadata AS
            SELECT 
                'financial_data' as table_name,
                td.description as table_description,
                td.business_purpose,
                td.data_scale,
                (SELECT COUNT(*) FROM financial_data) as current_record_count
            FROM table_descriptions td
            WHERE td.table_name = 'financial_data'R++Ytablesqlite_sequencesqlite_sequence ��CREATE TABLE sqlite_sequence(name,seq)�]))�qtablefinancial_datafinancial_data �CREATE TABLE "financial_data" ("year" INTEGER, "month" INTEGER, "accounting_organization" INTEGER, "accounting_unit_name" TEXT, "account_code" INTEGER, "account_full_name" TEXT, "account_name" TEXT, "opening_debit_amount" REAL, "opening_credit_amount" INTEGER, "account_direction" TEXT, "project_id" TEXT, "project_code" TEXT, "project_name" TEXT, "market_nature_id" TEXT, "tax_rate_id" TEXT, "tax_rate_name" TEXT, "business_format_id" TEXT, "financial_product_id" TEXT, "long_term_deferred_project_id" TEXT, "property_unit_id" TEXT, "cash_flow_project_id" TEXT, "municipal_enterprise_unit_id" TEXT, "bank_account_id" TEXT, "financial_institution_id" INTEGER, "bank_routing_number" INTEGER, "bank_name" TEXT, "debit_amount" REAL, "debit_cumulative" REAL, "credit_amount" REAL, "credit_cumulative" REAL, "balance" TEXT)  �� p/H]�6�o�;�-���_�
   - �w�
-                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         �b33�gtablefield_relationshipsfield_relationships ��CREATE TABLE field_relationships (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                table_name TEXT NOT NULL,
                primary_field TEXT NOT NULL,
                related_field TEXT NOT NULL,
                relationship_type TEXT NOT NULL,
                relationship_description TEXT,
                usage_example TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )�*))�tablebusiness_rulesbusiness_rules ��CREATE TABLE business_rules (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                table_name TEXT NOT NULL,
                rule_category TEXT NOT NULL,
                rule_description TEXT NOT NULL,
                sql_example TEXT,
                importance_level TEXT DEFAULT 'HIGH',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )GY3 indexsqlite_autoindex_column_descriptions_1column_descriptions �Ąl
33�{tablecolumn_descriptionscolumn_descriptions ��CREATE TABLE column_descriptions (
                table_name TEXT NOT NULL,
                column_name TEXT NOT NULL,
                chinese_name TEXT,
                description TEXT,
                data_type TEXT,
                business_rules TEXT,
                ai_understanding_points TEXT, field_category TEXT DEFAULT '', usage_scenarios TEXT DEFAULT '', common_values TEXT DEFAULT '', related_fields TEXT DEFAULT '', calculation_rules TEXT DEFAULT '', ai_prompt_hints TEXT DEFAULT '',
                PRIMARY KEY (table_name, column_name)
            )EW1 indexsqlite_autoindex_table_descriptions_1table_descriptions �?11�)tabletable_descriptionstable_descriptionsCREATE TABLE table_descriptions (
                table_name TEXT PRIMARY KEY,
                description TEXT NOT NULL,
                business_purpose TEXT,
                data_scale TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )       H]�6�o����~�	U�SQLite format 3   @      p/  ��  �   5                                                  .n�   �   �����< �� �                  �l
33�{tablecolumn_descriptionscolumn_descriptions ��CREATE TABLE column_descriptions (
                table_name TEXT NOT NULL,
                column_name TEXT NOT NULL,
                chinese_name TEXT,
                description TEXT,
                data_type TEXT,
                business_rules TEXT,
                ai_understanding_points TEXT, field_category TEXT DEFAULT '', usage_scenarios TEXT DEFAULT '', common_values TEXT DEFAULT '', related_fields TEXT DEFAULT '', calculation_rules TEXT DEFAULT '', ai_prompt_hints TEXT DEFAULT '',
                PRIMARY KEY (table_name, column_name)
            )�*))�tablebusiness_rulesbusiness_rules ��CREATE TABLE business_rules (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                table_name TEXT NOT NULL,
                rule_category TEXT NOT NULL,
                rule_description TEXT NOT NULL,
                sql_example TEXT,
                importance_level TEXT DEFAULT 'HIGH',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )GY3 indexsqlite_autoindex_column_descriptions_1column_descriptions �Ă?11�)tabletable_descriptionstable_descriptionsCREATE TABLE table_descriptions (
                table_name TEXT PRIMARY KEY,
                description TEXT NOT NULL,
                business_purpose TEXT,
                data_scale TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )EW1 indexsqlite_autoindex_table_descriptions_1table_descriptions �p
KK�[viewfinancial_data_columns_metadatafinancial_data_columns_metadataCREATE VIEW financial_data_columns_metadata AS
            SELECT 
                column_name,
                chinese_name,
                description,
                data_type,
                business_rules,
                ai_understanding_points
            FROM column_descriptions
            WHERE table_name = 'financial_data'
            ORDER BY 
                CASE 
                    WHEN column_name IN ('year', 'month') THEN 1
                    WHEN column_name LIKE 'accounting_%' THEN 2
                    WHEN column_name LIKE 'account_%' THEN 3
                    WHEN column_name LIKE '%amount%' OR column_name LIKE '%cumulative%' OR column_name = 'balance' THEN 4
                    WHEN column_name LIKE 'project_%' THEN 5
                    WHEN column_name LIKE 'bank_%' OR column_name LIKE 'financial_institution_%' THEN 6
                    ELSE 7
                END,
                column_name�S	EE�-viewfinancial_data_with_metadatafinancial_data_with_metadataCREATE VIEW financial_data_with_metadata AS
            SELECT 
                'financial_data' as table_name,
                td.description as table_description,
                td.business_purpose,
                td.data_scale,
                (SELECT COUNT(*) FROM financial_data) as current_record_count
            FROM table_descriptions td
            WHERE td.table_name = 'financial_data'R++Ytablesqlite_sequencesqlite_sequence ��CREATE TABLE sqlite_sequence(name,seq)�]))�qtablefinancial_datafinancial_data �CREATE TABLE "financial_data" ("year" INTEGER, "month" INTEGER, "accounting_organization" INTEGER, "accounting_unit_name" TEXT, "account_code" INTEGER, "account_full_name" TEXT, "account_name" TEXT, "opening_debit_amount" REAL, "opening_credit_amount" INTEGER, "account_direction" TEXT, "project_id" TEXT, "project_code" TEXT, "project_name" TEXT, "market_nature_id" TEXT, "tax_rate_id" TEXT, "tax_rate_name" TEXT, "business_format_id" TEXT, "financial_product_id" TEXT, "long_term_deferred_project_id" TEXT, "property_unit_id" TEXT, "cash_flow_project_id" TEXT, "municipal_enterprise_unit_id" TEXT, "bank_account_id" TEXT, "financial_institution_id" INTEGER, "bank_routing_number" INTEGER, "bank_name" TEXT, "debit_amount" REAL, "debit_cumulative" REAL, "credit_amount" REAL, "credit_cumulative" REAL, "balance"   ��
  ��    H]�6�o��P�"8m  ��  L  ��  �  �  �  �  �  �  �  �  �  �  �
  �  �  �
  �	  �  �  �  �  �  �  �  �  �   ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  �   �  �  �  �  �  �  �  �  �	  �
  �  �  �
  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �   �!  �"  �#  �$  �%  �&  �'  �(  �)  �*  �+  �,  �-  �.  �/  �0  �1  �2  �3  �4  �5  �6  �7  �8  �9  �:  �;  �<  �=  �>  �?  �@  �A  �B  �C  �D  �E  �F  �G  �H  �I  �J  �K  �L  �M  �N  �O  �P  �Q  �R  �S  �T  �U  �V  �W  �X  �Y  �Z  �[  �\  �]  �^  �_  �`  �a  �b  �c  �d  �e  �f  �g  �h  �i  �j  �k  �l  �n  �o  �p  �q  �r  �s  �t  �u  �v  �w  �x  �y  �z  �{  �|  �}  �~  �  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  �m  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��   行存款借       79035880-87f3-4a3b-ae13-6ab820525793 49777ac1-2845-8af5-1789-54cb4a72c850 G�
0� G�
0�招商银行股份有限公司深圳分行票据业务部�-4480.00000000�B��!�;%%


UU+�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       f5e52502-a987-41cd-9628-9e72ce7e508f 49777ac1-2845-8af5-1789-54cb4a72c850 G�
0� G�
0�招商银行股份有限公司深圳分行票据业务部p�-28879.00000000�D��!�;%%


UU#�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       7604f734-3f8a-4767-a39e-08183d22cb35 49777ac1-2845-8af5-1789-54cb4a72c850 G�
0� G�
0�招商银行股份有限公司深圳分行票据业务部@Iٙ����51.70000000�I��!�;%%


UU-�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       a4be09b9-c44f-4194-a848-2d1aa0d1911c 49777ac1-2845-8af5-1789-54cb4a72c850 G�
0� G�
0�招商银行股份有限公司深圳分行票据业务部A������-340922.95000000�,��!�;%%


UUO-�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       f5e52502-a987-41cd-9628-9e72ce7e508f 49777ac1-2845-8af5-1789-54cb4a72c850 G�
. G�
.招商银行深圳分行营业部T�-152735.00000000�/��!�;%%


UUO/�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       79035880-87f3-4a3b-ae13-6ab820525793 49777ac1-2845-8af5-1789-54cb4a72c850 G�
. G�
.招商银行深圳分行营业部 XX�-1325136.00000000�0��!�;%%


UUO)�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       5ec52f35-fa80-4795-9f3d-df0dd5006c6a 49777ac1-2845-8af5-1789-54cb4a72c850 G�
. G�
.招商银行深圳分行营业部@�Y�\)-1789.49000000�8��!�;%%


UUO+�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       3465b495-bfa8-41bd-99fe-49e8efb61e26 49777ac1-2845-8af5-1789-54cb4a72c850 G�
. G�
.招商银行深圳分行营业部A(۞z�H�ATz�G�814577.70000000�-��!�;%%


UUO%�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       7604f734-3f8a-4767-a39e-08183d22cb35 49777ac1-2845-8af5-1789-54cb4a72c850 G�
. G�
.招商银行深圳分行营业部@nz�G�240.19000000  ��    H]�6�o�Cٚ��-�q
    �w�
-                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      �))�stablequery_patternsquery_patterns ��CREATE TABLE query_patterns (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                pattern_name TEXT NOT NULL,
                pattern_description TEXT NOT NULL,
                natural_language_examples TEXT NOT NULL,
                sql_template TEXT NOT NULL,
                required_fields TEXT NOT NULL,
                business_scenario TEXT,
                difficulty_level TEXT DEFAULT 'MEDIUM',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )�b33�gtablefield_relationshipsfield_relationships ��CREATE TABLE field_relationships (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                table_name TEXT NOT NULL,
                primary_field TEXT NOT NULL,
                related_field TEXT NOT NULL,
                relationship_type TEXT NOT NULL,
                relationship_description TEXT,
                usage_example TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )�*))�tablebusiness_rulesbusiness_rules ��CREATE TABLE business_rules (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                table_name TEXT NOT NULL,
                rule_category TEXT NOT NULL,
                rule_description TEXT NOT NULL,
                sql_example TEXT,
                importance_level TEXT DEFAULT 'HIGH',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )GY3 indexsqlite_autoindex_column_descriptions_1column_descriptions �Ąl
33�{tablecolumn_descriptionscolumn_descriptions ��CREATE TABLE column_descriptions (
                table_name TEXT NOT NULL,
                column_name TEXT NOT NULL,
                chinese_name TEXT,
                description TEXT,
                data_type TEXT,
                business_rules TEXT,
                ai_understanding_points TEXT, field_category TEXT DEFAULT '', usage_scenarios TEXT DEFAULT '', common_values TEXT DEFAULT '', related_fields TEXT DEFAULT '', calculation_rules TEXT DEFAULT '', ai_prompt_hints TEXT DEFAULT '',
                PRIMARY KEY (table_name, column_name)
            )EW1 indexsqlite_autoindex_table_descriptions_1table_descriptions �?11�)tabletable_descriptionstable_descriptionsCREATE TABLE table_descriptions (
                table_name TEXT PRIMARY KEY,
                description TEXT NOT NULL,
                business_purpose TEXT,
                data_scale TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )  �� p/H]�6�o�$���ű�
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     H]�6�o�܇,7߱SQLite format 3   @      p/  ��  �
   6                                                  .n�   �   �����< �� �                  �l
33�{tablecolumn_descriptionscolumn_descriptions ��CREATE TABLE column_descriptions (
                table_name TEXT NOT NULL,
                column_name TEXT NOT NULL,
                chinese_name TEXT,
                description TEXT,
                data_type TEXT,
                business_rules TEXT,
                ai_understanding_points TEXT, field_category TEXT DEFAULT '', usage_scenarios TEXT DEFAULT '', common_values TEXT DEFAULT '', related_fields TEXT DEFAULT '', calculation_rules TEXT DEFAULT '', ai_prompt_hints TEXT DEFAULT '',
                PRIMARY KEY (table_name, column_name)
            )�*))�tablebusiness_rulesbusiness_rules ��CREATE TABLE business_rules (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                table_name TEXT NOT NULL,
                rule_category TEXT NOT NULL,
                rule_description TEXT NOT NULL,
                sql_example TEXT,
                importance_level TEXT DEFAULT 'HIGH',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )GY3 indexsqlite_autoindex_column_descriptions_1column_descriptions �Ă?11�)tabletable_descriptionstable_descriptionsCREATE TABLE table_descriptions (
                table_name TEXT PRIMARY KEY,
                description TEXT NOT NULL,
                business_purpose TEXT,
                data_scale TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )EW1 indexsqlite_autoindex_table_descriptions_1table_descriptions �p
KK�[viewfinancial_data_columns_metadatafinancial_data_columns_metadataCREATE VIEW financial_data_columns_metadata AS
            SELECT 
                column_name,
                chinese_name,
                description,
                data_type,
                business_rules,
                ai_understanding_points
            FROM column_descriptions
            WHERE table_name = 'financial_data'
            ORDER BY 
                CASE 
                    WHEN column_name IN ('year', 'month') THEN 1
                    WHEN column_name LIKE 'accounting_%' THEN 2
                    WHEN column_name LIKE 'account_%' THEN 3
                    WHEN column_name LIKE '%amount%' OR column_name LIKE '%cumulative%' OR column_name = 'balance' THEN 4
                    WHEN column_name LIKE 'project_%' THEN 5
                    WHEN column_name LIKE 'bank_%' OR column_name LIKE 'financial_institution_%' THEN 6
                    ELSE 7
                END,
                column_name�S	EE�-viewfinancial_data_with_metadatafinancial_data_with_metadataCREATE VIEW financial_data_with_metadata AS
            SELECT 
                'financial_data' as table_name,
                td.description as table_description,
                td.business_purpose,
                td.data_scale,
                (SELECT COUNT(*) FROM financial_data) as current_record_count
            FROM table_descriptions td
            WHERE td.table_name = 'financial_data'R++Ytablesqlite_sequencesqlite_sequence ��CREATE TABLE sqlite_sequence(name,seq)�]))�qtablefinancial_datafinancial_data �CREATE TABLE "financial_data" ("year" INTEGER, "month" INTEGER, "accounting_organization" INTEGER, "accounting_unit_name" TEXT, "account_code" INTEGER, "account_full_name" TEXT, "account_name" TEXT, "opening_debit_amount" REAL, "opening_credit_amount" INTEGER, "account_direction" TEXT, "project_id" TEXT, "project_code" TEXT, "project_name" TEXT, "market_nature_id" TEXT, "tax_rate_id" TEXT, "tax_rate_name" TEXT, "business_format_id" TEXT, "financial_product_id" TEXT, "long_term_deferred_project_id" TEXT, "property_unit_id" TEXT, "cash_flow_project_id" TEXT, "municipal_enterprise_unit_id" TEXT, "bank_account_id" TEXT, "financial_institution_id" INTEGER, "bank_routing_number" INTEGER, "bank_name" TEXT, "debit_amount" REAL, "debit_cumulative" REAL, "credit_amount" REAL, "credit_cumulative" REAL, "balance"   ��
  ��    H]�6�o��4v��x�w  ��  K  ��  �  �  �  �  �  �  �  �  �  �  �
  �  �  �
  �	  �  �  �  �  �  �  �  �  �   ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  �   �  �  �  �  �  �  �  �  �	  �
  �  �  �
  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �   �!  �"  �#  �$  �%  �&  �'  �(  �)  �*  �+  �,  �-  �.  �/  �0  �1  �2  �3  �4  �5  �6  �7  �8  �9  �:  �;  �<  �=  �>  �?  �@  �A  �B  �C  �D  �E  �F  �G  �H  �I  �J  �K  �L  �M  �N  �O  �P  �Q  �R  �S  �T  �U  �V  �W  �X  �Y  �Z  �[  �\  �]  �^  �_  �`  �a  �b  �c  �d  �e  �f  �g  �h  �i  �j  �k  �l  �n  �o  �p  �q  �r  �s  �t  �u  �v  �w  �x  �y  �z  �{  �|  �}  �~  �  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  �m  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��   行存款借       79035880-87f3-4a3b-ae13-6ab820525793 49777ac1-2845-8af5-1789-54cb4a72c850 G�
0� G�
0�招商银行股份有限公司深圳分行票据业务部�-4480.00000000�B��!�;%%


UU+�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       f5e52502-a987-41cd-9628-9e72ce7e508f 49777ac1-2845-8af5-1789-54cb4a72c850 G�
0� G�
0�招商银行股份有限公司深圳分行票据业务部p�-28879.00000000�D��!�;%%


UU#�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       7604f734-3f8a-4767-a39e-08183d22cb35 49777ac1-2845-8af5-1789-54cb4a72c850 G�
0� G�
0�招商银行股份有限公司深圳分行票据业务部@Iٙ����51.70000000�I��!�;%%


UU-�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       a4be09b9-c44f-4194-a848-2d1aa0d1911c 49777ac1-2845-8af5-1789-54cb4a72c850 G�
0� G�
0�招商银行股份有限公司深圳分行票据业务部A������-340922.95000000�,��!�;%%


UUO-�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       f5e52502-a987-41cd-9628-9e72ce7e508f 49777ac1-2845-8af5-1789-54cb4a72c850 G�
. G�
.招商银行深圳分行营业部T�-152735.00000000�/��!�;%%


UUO/�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       79035880-87f3-4a3b-ae13-6ab820525793 49777ac1-2845-8af5-1789-54cb4a72c850 G�
. G�
.招商银行深圳分行营业部 XX�-1325136.00000000�0��!�;%%


UUO)�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       5ec52f35-fa80-4795-9f3d-df0dd5006c6a 49777ac1-2845-8af5-1789-54cb4a72c850 G�
. G�
.招商银行深圳分行营业部@�Y�\)-1789.49000000�8��!�;%%


UUO+�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       3465b495-bfa8-41bd-99fe-49e8efb61e26 49777ac1-2845-8af5-1789-54cb4a72c850 G�
. G�
.招商银行深圳分行营业部A(۞z�H�ATz�G�814577.70000000�-��!�;%%


UUO%�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       7604f734-3f8a-4767-a39e-08183d22cb35 49777ac1-2845-8af5-1789-54cb4a72c850 G�
. G�
.招商银行深圳分行营业部@nz�G�240.19000000  ��    H]�6�o�\ ���
    �w�
-                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             �11�/tabledata_quality_rulesdata_quality_rules ��CREATE TABLE data_quality_rules (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                table_name TEXT NOT NULL,
                field_name TEXT NOT NULL,
                rule_type TEXT NOT NULL,
                rule_description TEXT NOT NULL,
                validation_sql TEXT,
                error_message TEXT,
                severity_level TEXT DEFAULT 'WARNING',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )�))�stablequery_patternsquery_patterns ��CREATE TABLE query_patterns (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                pattern_name TEXT NOT NULL,
                pattern_description TEXT NOT NULL,
                natural_language_examples TEXT NOT NULL,
                sql_template TEXT NOT NULL,
                required_fields TEXT NOT NULL,
                business_scenario TEXT,
                difficulty_level TEXT DEFAULT 'MEDIUM',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )�b33�gtablefield_relationshipsfield_relationships ��CREATE TABLE field_relationships (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                table_name TEXT NOT NULL,
                primary_field TEXT NOT NULL,
                related_field TEXT NOT NULL,
                relationship_type TEXT NOT NULL,
                relationship_description TEXT,
                usage_example TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )�*))�tablebusiness_rulesbusiness_rules ��CREATE TABLE business_rules (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                table_name TEXT NOT NULL,
                rule_category TEXT NOT NULL,
                rule_description TEXT NOT NULL,
                sql_example TEXT,
                importance_level TEXT DEFAULT 'HIGH',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )GY3 indexsqlite_autoindex_column_descriptions_1column_descriptions �Ąl
33�{tablecolumn_descriptionscolumn_descriptions ��CREATE TABLE column_descriptions (
                table_name TEXT NOT NULL,
                column_name TEXT NOT NULL,
                chinese_name TEXT,
                description TEXT,
                data_type TEXT,
                business_rules TEXT,
                ai_understanding_points TEXT, field_category TEXT DEFAULT '', usage_scenarios TEXT DEFAULT '', common_values TEXT DEFAULT '', related_fields TEXT DEFAULT '', calculation_rules TEXT DEFAULT '', ai_prompt_hints TEXT DEFAULT '',
                PRIMARY KEY (table_name, column_name)
            )EW1 indexsqlite_autoindex_table_descriptions_1table_descriptions �?11�)tabletable_descriptionstable_descriptionsCREATE TABLE table_descriptions (
                table_name TEXT PRIMARY KEY,
                description TEXT NOT NULL,
                business_purpose TEXT,
                data_scale TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )  �� p/H]�6�o�z��4f�{V
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     H]�6�o�"�����SQLite format 3   @      p/  ��  �	   7                                                  .n�   �   �����< �� �                  �l
33�{tablecolumn_descriptionscolumn_descriptions ��CREATE TABLE column_descriptions (
                table_name TEXT NOT NULL,
                column_name TEXT NOT NULL,
                chinese_name TEXT,
                description TEXT,
                data_type TEXT,
                business_rules TEXT,
                ai_understanding_points TEXT, field_category TEXT DEFAULT '', usage_scenarios TEXT DEFAULT '', common_values TEXT DEFAULT '', related_fields TEXT DEFAULT '', calculation_rules TEXT DEFAULT '', ai_prompt_hints TEXT DEFAULT '',
                PRIMARY KEY (table_name, column_name)
            )�*))�tablebusiness_rulesbusiness_rules ��CREATE TABLE business_rules (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                table_name TEXT NOT NULL,
                rule_category TEXT NOT NULL,
                rule_description TEXT NOT NULL,
                sql_example TEXT,
                importance_level TEXT DEFAULT 'HIGH',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )GY3 indexsqlite_autoindex_column_descriptions_1column_descriptions �Ă?11�)tabletable_descriptionstable_descriptionsCREATE TABLE table_descriptions (
                table_name TEXT PRIMARY KEY,
                description TEXT NOT NULL,
                business_purpose TEXT,
                data_scale TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )EW1 indexsqlite_autoindex_table_descriptions_1table_descriptions �p
KK�[viewfinancial_data_columns_metadatafinancial_data_columns_metadataCREATE VIEW financial_data_columns_metadata AS
            SELECT 
                column_name,
                chinese_name,
                description,
                data_type,
                business_rules,
                ai_understanding_points
            FROM column_descriptions
            WHERE table_name = 'financial_data'
            ORDER BY 
                CASE 
                    WHEN column_name IN ('year', 'month') THEN 1
                    WHEN column_name LIKE 'accounting_%' THEN 2
                    WHEN column_name LIKE 'account_%' THEN 3
                    WHEN column_name LIKE '%amount%' OR column_name LIKE '%cumulative%' OR column_name = 'balance' THEN 4
                    WHEN column_name LIKE 'project_%' THEN 5
                    WHEN column_name LIKE 'bank_%' OR column_name LIKE 'financial_institution_%' THEN 6
                    ELSE 7
                END,
                column_name�S	EE�-viewfinancial_data_with_metadatafinancial_data_with_metadataCREATE VIEW financial_data_with_metadata AS
            SELECT 
                'financial_data' as table_name,
                td.description as table_description,
                td.business_purpose,
                td.data_scale,
                (SELECT COUNT(*) FROM financial_data) as current_record_count
            FROM table_descriptions td
            WHERE td.table_name = 'financial_data'R++Ytablesqlite_sequencesqlite_sequence ��CREATE TABLE sqlite_sequence(name,seq)�]))�qtablefinancial_datafinancial_data �CREATE TABLE "financial_data" ("year" INTEGER, "month" INTEGER, "accounting_organization" INTEGER, "accounting_unit_name" TEXT, "account_code" INTEGER, "account_full_name" TEXT, "account_name" TEXT, "opening_debit_amount" REAL, "opening_credit_amount" INTEGER, "account_direction" TEXT, "project_id" TEXT, "project_code" TEXT, "project_name" TEXT, "market_nature_id" TEXT, "tax_rate_id" TEXT, "tax_rate_name" TEXT, "business_format_id" TEXT, "financial_product_id" TEXT, "long_term_deferred_project_id" TEXT, "property_unit_id" TEXT, "cash_flow_project_id" TEXT, "municipal_enterprise_unit_id" TEXT, "bank_account_id" TEXT, "financial_institution_id" INTEGER, "bank_routing_number" INTEGER, "bank_name" TEXT, "debit_amount" REAL, "debit_cumulative" REAL, "credit_amount" REAL, "credit_cumulative" REAL, "balance"   ��
  ��    H]�6�o���h)  ��  J  ��  �  �  �  �  �  �  �  �  �  �  �
  �  �  �
  �	  �  �  �  �  �  �  �  �  �   ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  �   �  �  �  �  �  �  �  �  �	  �
  �  �  �
  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �   �!  �"  �#  �$  �%  �&  �'  �(  �)  �*  �+  �,  �-  �.  �/  �0  �1  �2  �3  �4  �5  �6  �7  �8  �9  �:  �;  �<  �=  �>  �?  �@  �A  �B  �C  �D  �E  �F  �G  �H  �I  �J  �K  �L  �M  �N  �O  �P  �Q  �R  �S  �T  �U  �V  �W  �X  �Y  �Z  �[  �\  �]  �^  �_  �`  �a  �b  �c  �d  �e  �f  �g  �h  �i  �j  �k  �l  �n  �o  �p  �q  �r  �s  �t  �u  �v  �w  �x  �y  �z  �{  �|  �}  �~  �  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  �m  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��   行存款借       79035880-87f3-4a3b-ae13-6ab820525793 49777ac1-2845-8af5-1789-54cb4a72c850 G�
0� G�
0�招商银行股份有限公司深圳分行票据业务部�-4480.00000000�B��!�;%%


UU+�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       f5e52502-a987-41cd-9628-9e72ce7e508f 49777ac1-2845-8af5-1789-54cb4a72c850 G�
0� G�
0�招商银行股份有限公司深圳分行票据业务部p�-28879.00000000�D��!�;%%


UU#�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       7604f734-3f8a-4767-a39e-08183d22cb35 49777ac1-2845-8af5-1789-54cb4a72c850 G�
0� G�
0�招商银行股份有限公司深圳分行票据业务部@Iٙ����51.70000000�I��!�;%%


UU-�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       a4be09b9-c44f-4194-a848-2d1aa0d1911c 49777ac1-2845-8af5-1789-54cb4a72c850 G�
0� G�
0�招商银行股份有限公司深圳分行票据业务部A������-340922.95000000�,��!�;%%


UUO-�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       f5e52502-a987-41cd-9628-9e72ce7e508f 49777ac1-2845-8af5-1789-54cb4a72c850 G�
. G�
.招商银行深圳分行营业部T�-152735.00000000�/��!�;%%


UUO/�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       79035880-87f3-4a3b-ae13-6ab820525793 49777ac1-2845-8af5-1789-54cb4a72c850 G�
. G�
.招商银行深圳分行营业部 XX�-1325136.00000000�0��!�;%%


UUO)�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       5ec52f35-fa80-4795-9f3d-df0dd5006c6a 49777ac1-2845-8af5-1789-54cb4a72c850 G�
. G�
.招商银行深圳分行营业部@�Y�\)-1789.49000000�8��!�;%%


UUO+�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       3465b495-bfa8-41bd-99fe-49e8efb61e26 49777ac1-2845-8af5-1789-54cb4a72c850 G�
. G�
.招商银行深圳分行营业部A(۞z�H�ATz�G�814577.70000000�-��!�;%%


UUO%�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       7604f734-3f8a-4767-a39e-08183d22cb35 49777ac1-2845-8af5-1789-54cb4a72c850 G�
. G�
.招商银行深圳分行营业部@nz�G�240.19000000  ��    H]�6�o��qpȢ���
   	 �w�
-                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                             �k33�ytableai_prompt_templatesai_prompt_templates ��CREATE TABLE ai_prompt_templates (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                template_name TEXT NOT NULL,
                template_type TEXT NOT NULL,
                template_content TEXT NOT NULL,
                usage_scenario TEXT,
                priority_level INTEGER DEFAULT 5,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )�11�/tabledata_quality_rulesdata_quality_rules ��CREATE TABLE data_quality_rules (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                table_name TEXT NOT NULL,
                field_name TEXT NOT NULL,
                rule_type TEXT NOT NULL,
                rule_description TEXT NOT NULL,
                validation_sql TEXT,
                error_message TEXT,
                severity_level TEXT DEFAULT 'WARNING',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )�))�stablequery_patternsquery_patterns ��CREATE TABLE query_patterns (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                pattern_name TEXT NOT NULL,
                pattern_description TEXT NOT NULL,
                natural_language_examples TEXT NOT NULL,
                sql_template TEXT NOT NULL,
                required_fields TEXT NOT NULL,
                business_scenario TEXT,
                difficulty_level TEXT DEFAULT 'MEDIUM',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )�b33�gtablefield_relationshipsfield_relationships ��CREATE TABLE field_relationships (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                table_name TEXT NOT NULL,
                primary_field TEXT NOT NULL,
                related_field TEXT NOT NULL,
                relationship_type TEXT NOT NULL,
                relationship_description TEXT,
                usage_example TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )�*))�tablebusiness_rulesbusiness_rules ��CREATE TABLE business_rules (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                table_name TEXT NOT NULL,
                rule_category TEXT NOT NULL,
                rule_description TEXT NOT NULL,
                sql_example TEXT,
                importance_level TEXT DEFAULT 'HIGH',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )GY3 indexsqlite_autoindex_column_descriptions_1column_descriptions �Ąl
33�{tablecolumn_descriptionscolumn_descriptions ��CREATE TABLE column_descriptions (
                table_name TEXT NOT NULL,
                column_name TEXT NOT NULL,
                chinese_name TEXT,
                description TEXT,
                data_type TEXT,
                business_rules TEXT,
                ai_understanding_points TEXT, field_category TEXT DEFAULT '', usage_scenarios TEXT DEFAULT '', common_values TEXT DEFAULT '', related_fields TEXT DEFAULT '', calculation_rules TEXT DEFAULT '', ai_prompt_hints TEXT DEFAULT '',
                PRIMARY KEY (table_name, column_name)
            )EW1 indexsqlite_autoindex_table_descriptions_1table_descriptions �?11�)tabletable_descriptionstable_descriptionsCREATE TABLE table_descriptions (
                table_name TEXT PRIMARY KEY,
                description TEXT NOT NULL,
                business_purpose TEXT,
                data_scale TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )  �� p/H]�6�o��uQ�m�-
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                ��    H]�6�o���7���z   �   ��
f�	<
l	��@����n�P�J�2 l                                                    �C)G5Uaafinancial_datalong_term_deferred_project_id长期待摊项目ID长期待摊费用项目的标识符TEXT用于长期待摊费用的核算和分摊用于长期待摊费用的核算和分摊�)5)===financial_datafinancial_product_id金融产品ID金融产品的标识符TEXT关联金融产品信息关联金融产品信息�)1=OOfinancial_databusiness_format_id业态ID业务形态的标识符TEXT用于按业务形态分类统计用于按业务形态分类统计�	)'%77efinancial_datatax_rate_name税率名称税率的名称描述TEXT税率的具体名称税率的具体名称，如"13%增值税"等x)#11[financial_datatax_rate_id税率ID税率的标识符TEXT关联税率信息关联税率信息，用于税务分析�)-)=OOfinancial_datamarket_nature_id市场性质ID市场性质的标识符TEXT用于按市场性质分类分析用于按市场性质分类分析�)%%77afinancial_dataproject_name项目名称项目的名称描述TEXT项目的具体名称项目的具体名称，用于报表展示� )%%+7afinancial_dataproject_code项目编号项目的编码TEXT项目的业务编号项目的业务编号，便于项目管理�)!=Oafinancial_dataproject_id项目ID项目的唯一标识符TEXT用于项目维度的财务分析用于项目维度的财务分析和核算�)7ggfinancial_databalance余额科目的期末余额TEXT期末余额，可能包含借贷方向信息期末余额，可能包含借贷方向信息�8)/%aOyfinancial_datacredit_cumulative贷方累计从年初到当期的贷方累计发生额REAL年初至今的贷方累计金额年初至今的贷方累计金额，用于年度分析�7
)-%aOyfinancial_datadebit_cumulative借方累计从年初到当期的借方累计发生额REAL年初至今的借方累计金额年初至今的借方累计金额，用于年度分析�,	)'%C7�financial_datacredit_amount贷方金额当期发生的贷方金额REAL当期贷方发生额当期贷方发生额，反映当期业务活动对科目的贷方影响�+	)%%C7�financial_datadebit_amount借方金额当期发生的借方金额REAL当期借方发生额当期借方发生额，反映当期业务活动对科目的借方影响�C
	)71UC�financial_dataopening_credit_amount期初贷方金额会计期间开始时的贷方余额INTEGER期初余额的贷方部分期初余额的贷方部分，与期初借方金额配合使用�5	)51UCyfinancial_dataopening_debit_amount期初借方金额会计期间开始时的借方余额REAL期初余额的借方部分期初余额的借方部分，用于计算期末余额�M	)/%O;�Gfinancial_dataaccount_direction科目方向会计科目的借贷方向属性TEXT取值: "借" 或 "贷"决定科目的正常余额方向，借方科目增加记借方，贷方科目增加记贷方�J	)%%Cw�financial_dataaccount_name科目名称会计科目的简化名称TEXT通常与account_full_name相同或为其简化版本科目的简称，通常与account_full_name相同或为其简化版本�)/%C7sfinancial_dataaccount_full_name科目全称会计科目的完整名称TEXT科目的详细描述科目的详细描述，用于财务报表和分析�=	)%%C[�financial_dataaccount_code科目编号会计科目的数字编码INTEGER遵循会计准则的科目编码规则会计科目的唯一标识，遵循会计准则的科目编码规则�6	)51CC�financial_dataaccounting_unit_name核算单位名称核算单位的完整名称TEXT核算主体的具体名称核算主体的具体名称，用于报表展示和数据筛选�2);%COyfinancial_dataaccounting_organization核算组织核算组织的代码标识INTEGER用于区分不同的核算主体用于区分不同的核算主体，进行分组统计�	)O1�financial_datamonth月财务数据所属的会计月份INTEGER取值范围: 1-12与year字段配合，精确定位财务数据的时间点�)O=yfinancial_datayear年财务数据所属的会计年度INTEGER用于时间序列分析用于时间序列分析，按年度统计财务�  ��  ��    H]�6�o�aɅ�\D��

  _ �
��	��]�E� _

                                                           �M)'%C7�%k%I1Ufinancial_datacredit_amount贷方金额当期发生的贷方金额REAL当期贷方发生额当期贷方发生额，反映当期业务活动对科目的贷方影响金额维度收入分析,贷方发生额统计,收益核算正数金额debit_amount,credit_cumulative贷方金额汇总收入类科目必须使用此字段�X)%%C7�%w%I1afinancial_datadebit_amount借方金额当期发生的借方金额REAL当期借方发生额当期借方发生额，反映当期业务活动对科目的借方影响金额维度费用支出分析,成本核算,借方发生额统计正数金额credit_amount,debit_cumulative借方金额汇总成本费用类科目必须使用此字段  u)/%aOyfinancial_datacredit_cumulative贷方累计从年初到当期的贷方累计发生额REAL年初至今的贷方累计金额年初至今的贷方累计金额，用于年度分析   �)-%aOyfinancial_datadebit_cumulative借方累计从年初到当期的借方累计发生额REAL年初至今的借方累计金额年初至今的借方累计金额，用于年度分析�C
	)71UC�financial_dataopening_credit_amount期初贷方金额会计期间开始时的贷方余额INTEGER期初余额的贷方部分期初余额的贷方部分，与期初借方金额配合使用�5	)51UCyfinancial_dataopening_debit_amount期初借方金额会计期间开始时的借方余额REAL期初余额的借方部分期初余额的借方部分，用于计算期末余额�])/%O;�G%}%1Ofinancial_dataaccount_direction科目方向会计科目的借贷方向属性TEXT取值: "借" 或 "贷"决定科目的正常余额方向，借方科目增加记借方，贷方科目增加记贷方科目维度借贷方向判断,会计平衡验证,科目性质分析借,贷account_code借贷平衡验证确定科目的正常余额方向�)%%Cw�%}eI1[financial_dataaccount_name科目名称会计科目的简化名称TEXT通常与account_full_name相同或为其简化版本科目的简称，通常与account_full_name相同或为其简化版本科目维度科目名称查询,财务科目分析,会计科目管理银行存款,主营业务收入,管理费用account_code,account_direction科目名称匹配用于科目名称显示和模糊查询�)/%C7sfinancial_dataaccount_full_name科目全称会计科目的完整名称TEXT科目的详细描述科目的详细描述，用于财务报表和分析�w)%%C[�%}3I1gfinancial_dataaccount_code科目编号会计科目的数字编码INTEGER遵循会计准则的科目编码规则会计科目的唯一标识，遵循会计准则的科目编码规则科目维度科目分类查询,财务报表编制,科目余额分析1001,1002,6001,6401account_name,account_direction科目分类规则根据科目编号确定使用的金额字段�q)51CC�%}U;7Ofinancial_dataaccounting_unit_name核算单位名称核算单位的完整名称TEXT核算主体的具体名称核算主体的具体名称，用于报表展示和数据筛选组织维度具体单位分析,单位绩效对比,单位财务状况REITs专项计划基金,物业公司accounting_organization单位级明细查询用于单位名称显示和筛选�B);%COy%w#5+Cfinancial_dataaccounting_organization核算组织核算组织的代码标识INTEGER用于区分不同的核算主体用于区分不同的核算主体，进行分组统计组织维度组织架构分析,多组织对比,组织绩效评估101,102,103accounting_unit_name组织级汇总用于组织分组和筛选�)O1�%e1Ofinancial_datamonth月财务数据所属的会计月份INTEGER取值范围: 1-12与year字段配合，精确定位财务数据的时间点时间维度月度财务分析,季度汇总,月度对比1-12year月度汇总计算用于月度分组和时间筛选�')O=y%�)1Ofinancial_datayear年财务数据所属的会计年度INTEGER用于时间序列分析用于时间序列分析，按年度统计财务数据时间维度年度财务分析,时间序列对比,跨年度趋势分析2024,2023,2022month年度汇总计算用于年度分组和时间筛选  � p/H]�6�o�&W�f�N�
 � /�~
o	�	c�T�7��A�
�
@��                                                                                                                                                                                                                                                                                                                                                                                                                                                �N)/%aOy%}%'1Ofinancial_datacredit_cumulative贷方累计从年初到当期的贷方累计发生额REAL年初至今的贷方累计金额年初至今的贷方累计金额，用于年度分析金额维度年度累计收入,累计收益分析,年度收入统计正数金额credit_amount年度累计汇总年初至今的贷方累计金额�L
)-%aOy%}%%1Ofinancial_datadebit_cumulative借方累计从年初到当期的借方累计发生额REAL年初至今的借方累计金额年初至今的借方累计金额，用于年度分析金额维度年度累计分析,累计费用统计,年度成本分析正数金额debit_amount年度累计汇总年初至今的借方累计金额�i)7gg%wQA7�financial_databalance余额科目的期末余额TEXT期末余额，可能包含借贷方向信息期末余额，可能包含借贷方向信息金额维度余额分析,资产负债表编制,期末余额查询正负数金额,可能包含方向debit_amount,credit_amountCAST(balance AS REAL)资产负债类科目必须使用此字段,需类型转换�C)G5Uaafinancial_datalong_term_deferred_project_id长期待摊项目ID长期待摊费用项目的标识符TEXT用于长期待摊费用的核算和分摊用于长期待摊费用的核算和分摊�)5)===financial_datafinancial_product_id金融产品ID金融产品的标识符TEXT关联金融产品信息关联金融产品信息�)1=OOfinancial_databusiness_format_id业态ID业务形态的标识符TEXT用于按业务形态分类统计用于按业务形态分类统计�	)'%77efinancial_datatax_rate_name税率名称税率的名称描述TEXT税率的具体名称税率的具体名称，如"13%增值税"等x)#11[financial_datatax_rate_id税率ID税率的标识符TEXT关联税率信息关联税率信息，用于税务分析�)-)=OOfinancial_datamarket_nature_id市场性质ID市场性质的标识符TEXT用于按市场性质分类分析用于按市场性质分类分析�)%%77afinancial_dataproject_name项目名称项目的名称描述TEXT项目的具体名称项目的具体名称，用于报表展示� )%%+7afinancial_dataproject_code项目编号项目的编码TEXT项目的业务编号项目的业务编号，便于项目管理�)!=Oafinancial_dataproject_id项目ID项目的唯一标识符TEXT用于项目维度的财务分析用于项目维度的财务分析和核算   �)7ggfinancial_databalance余额科目的期末余额TEXT期末余额，可能包含借贷方向信息期末余额，可能包含借贷方向信息�)%+7kfinancial_databank_name银行名称银行的名称TEXT具体的银行名称具体的银行名称，如"中国工商银行"�
)37IIfinancial_databank_routing_number联行号银行的联行号码INTEGER银行间清算的标识号码银行间清算的标识号码�)=)=CCfinancial_datafinancial_institution_id金融机构ID金融机构的标识符INTEGER标识具体的金融机构标识具体的金融机构�)+)=OOfinancial_databank_account_id银行账号ID银行账户的标识符TEXT关联具体的银行账户信息关联具体的银行账户信息�6)E5UUUfinancial_datamunicipal_enterprise_unit_id市属国企单位ID市属国有企业单位的标识符TEXT特定于国有企业的分类标识特定于国有企业的分类标识�1)55O[[financial_datacash_flow_project_id现金流量项目ID现金流量表项目的标识符TEXT用于现金流量表的编制和分析用于现金流量表的编制和分析�<)-)aggfinancial_dataproperty_unit_id楼盘房号ID房地产项目中具体房号的标识符TEXT房地产业务中的具体房产单位标识房地产业务中的具体房产单位标识       H]�6�o��d��`� �SQLite format 3   @      p/  ��  �   7                                                  .n�   �   �����< �� �                  �l
33�{tablecolumn_descriptionscolumn_descriptions ��CREATE TABLE column_descriptions (
                table_name TEXT NOT NULL,
                column_name TEXT NOT NULL,
                chinese_name TEXT,
                description TEXT,
                data_type TEXT,
                business_rules TEXT,
                ai_understanding_points TEXT, field_category TEXT DEFAULT '', usage_scenarios TEXT DEFAULT '', common_values TEXT DEFAULT '', related_fields TEXT DEFAULT '', calculation_rules TEXT DEFAULT '', ai_prompt_hints TEXT DEFAULT '',
                PRIMARY KEY (table_name, column_name)
            )�*))�tablebusiness_rulesbusiness_rules ��CREATE TABLE business_rules (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                table_name TEXT NOT NULL,
                rule_category TEXT NOT NULL,
                rule_description TEXT NOT NULL,
                sql_example TEXT,
                importance_level TEXT DEFAULT 'HIGH',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )GY3 indexsqlite_autoindex_column_descriptions_1column_descriptions �Ă?11�)tabletable_descriptionstable_descriptionsCREATE TABLE table_descriptions (
                table_name TEXT PRIMARY KEY,
                description TEXT NOT NULL,
                business_purpose TEXT,
                data_scale TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )EW1 indexsqlite_autoindex_table_descriptions_1table_descriptions �p
KK�[viewfinancial_data_columns_metadatafinancial_data_columns_metadataCREATE VIEW financial_data_columns_metadata AS
            SELECT 
                column_name,
                chinese_name,
                description,
                data_type,
                business_rules,
                ai_understanding_points
            FROM column_descriptions
            WHERE table_name = 'financial_data'
            ORDER BY 
                CASE 
                    WHEN column_name IN ('year', 'month') THEN 1
                    WHEN column_name LIKE 'accounting_%' THEN 2
                    WHEN column_name LIKE 'account_%' THEN 3
                    WHEN column_name LIKE '%amount%' OR column_name LIKE '%cumulative%' OR column_name = 'balance' THEN 4
                    WHEN column_name LIKE 'project_%' THEN 5
                    WHEN column_name LIKE 'bank_%' OR column_name LIKE 'financial_institution_%' THEN 6
                    ELSE 7
                END,
                column_name�S	EE�-viewfinancial_data_with_metadatafinancial_data_with_metadataCREATE VIEW financial_data_with_metadata AS
            SELECT 
                'financial_data' as table_name,
                td.description as table_description,
                td.business_purpose,
                td.data_scale,
                (SELECT COUNT(*) FROM financial_data) as current_record_count
            FROM table_descriptions td
            WHERE td.table_name = 'financial_data'R++Ytablesqlite_sequencesqlite_sequence ��CREATE TABLE sqlite_sequence(name,seq)�]))�qtablefinancial_datafinancial_data �CREATE TABLE "financial_data" ("year" INTEGER, "month" INTEGER, "accounting_organization" INTEGER, "accounting_unit_name" TEXT, "account_code" INTEGER, "account_full_name" TEXT, "account_name" TEXT, "opening_debit_amount" REAL, "opening_credit_amount" INTEGER, "account_direction" TEXT, "project_id" TEXT, "project_code" TEXT, "project_name" TEXT, "market_nature_id" TEXT, "tax_rate_id" TEXT, "tax_rate_name" TEXT, "business_format_id" TEXT, "financial_product_id" TEXT, "long_term_deferred_project_id" TEXT, "property_unit_id" TEXT, "cash_flow_project_id" TEXT, "municipal_enterprise_unit_id" TEXT, "bank_account_id" TEXT, "financial_institution_id" INTEGER, "bank_routing_number" INTEGER, "bank_name" TEXT, "debit_amount" REAL, "debit_cumulative" REAL, "credit_amount" REAL, "credit_cumulative" REAL, "balance"   ��
  ��    H]�6�o�����[  ��  H  ��  �  �  �  �  �  �  �  �  �  �  �
  �  �  �
  �	  �  �  �  �  �  �  �  �  �   ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  �   �  �  �  �  �  �  �  �  �	  �
  �  �  �
  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �   �!  �"  �#  �$  �%  �&  �'  �(  �)  �*  �+  �,  �-  �.  �/  �0  �1  �2  �3  �4  �5  �6  �7  �8  �9  �:  �;  �<  �=  �>  �?  �@  �A  �B  �C  �D  �E  �F  �G  �H  �I  �J  �K  �L  �M  �N  �O  �P  �Q  �R  �S  �T  �U  �V  �W  �X  �Y  �Z  �[  �\  �]  �^  �_  �`  �a  �b  �c  �d  �e  �f  �g  �h  �i  �j  �k  �l  �n  �o  �p  �q  �r  �s  �t  �u  �v  �w  �x  �y  �z  �{  �|  �}  �~  �  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  �m  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��   行存款借       79035880-87f3-4a3b-ae13-6ab820525793 49777ac1-2845-8af5-1789-54cb4a72c850 G�
0� G�
0�招商银行股份有限公司深圳分行票据业务部�-4480.00000000�B��!�;%%


UU+�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       f5e52502-a987-41cd-9628-9e72ce7e508f 49777ac1-2845-8af5-1789-54cb4a72c850 G�
0� G�
0�招商银行股份有限公司深圳分行票据业务部p�-28879.00000000�D��!�;%%


UU#�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       7604f734-3f8a-4767-a39e-08183d22cb35 49777ac1-2845-8af5-1789-54cb4a72c850 G�
0� G�
0�招商银行股份有限公司深圳分行票据业务部@Iٙ����51.70000000�I��!�;%%


UU-�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       a4be09b9-c44f-4194-a848-2d1aa0d1911c 49777ac1-2845-8af5-1789-54cb4a72c850 G�
0� G�
0�招商银行股份有限公司深圳分行票据业务部A������-340922.95000000�,��!�;%%


UUO-�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       f5e52502-a987-41cd-9628-9e72ce7e508f 49777ac1-2845-8af5-1789-54cb4a72c850 G�
. G�
.招商银行深圳分行营业部T�-152735.00000000�/��!�;%%


UUO/�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       79035880-87f3-4a3b-ae13-6ab820525793 49777ac1-2845-8af5-1789-54cb4a72c850 G�
. G�
.招商银行深圳分行营业部 XX�-1325136.00000000�0��!�;%%


UUO)�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       5ec52f35-fa80-4795-9f3d-df0dd5006c6a 49777ac1-2845-8af5-1789-54cb4a72c850 G�
. G�
.招商银行深圳分行营业部@�Y�\)-1789.49000000�8��!�;%%


UUO+�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       3465b495-bfa8-41bd-99fe-49e8efb61e26 49777ac1-2845-8af5-1789-54cb4a72c850 G�
. G�
.招商银行深圳分行营业部A(۞z�H�ATz�G�814577.70000000�-��!�;%%


UUO%�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       7604f734-3f8a-4767-a39e-08183d22cb35 49777ac1-2845-8af5-1789-54cb4a72c850 G�
. G�
.招商银行深圳分行营业部@nz�G�240.19000000  ��    H]�6�o�������&   �   ���z~
r�m���                                                                                                                                                                                                                                                                                                                                                                                                �E
 )1�i�93financial_data组织筛选规则按组织查询时，可使用accounting_organization(数字编号)或accounting_unit_name(名称)进行筛选SELECT accounting_organization, accounting_unit_name, COUNT(*) as 记录数 FROM financial_data GROUP BY accounting_organization, accounting_unit_nameHIGH2025-07-28 06:02:04�
 )1�I�q3financial_data时间筛选规则查询特定时间段时，必须同时使用year和month字段进行筛选，月份范围1-12SELECT year, month, COUNT(*) as 记录数 FROM financial_data WHERE year = 2024 AND month = 9 GROUP BY year, monthHIGH2025-07-28 06:02:04�n

 )1��i3financial_data负债科目规则流动负债(2001-2199)，非流动负债(2201-2999)，所有负债类科目必须使用balance字段，且需要CAST转换SELECT account_code, account_name, SUM(CAST(balance AS REAL)) as 负债余额 FROM financial_data WHERE account_code BETWEEN 2001 AND 2999 GROUP BY account_code, account_nameCRITICAL2025-07-28 06:02:04�n	
 )1��i3financial_data资产科目规则流动资产(1001-1199)，非流动资产(1201-1999)，所有资产类科目必须使用balance字段，且需要CAST转换SELECT account_code, account_name, SUM(CAST(balance AS REAL)) as 资产余额 FROM financial_data WHERE account_code BETWEEN 1001 AND 1999 GROUP BY account_code, account_nameCRITICAL2025-07-28 06:02:04�
 )=�O�W3financial_data费用科目细分规则主营业务成本(6401-6499)，销售费用(6601-6699)，管理费用(6602-6699)，财务费用(6603-6699)，所有费用类科目必须使用debit_amount字段SELECT account_code, account_name, SUM(debit_amount) as 费用金额 FROM financial_data WHERE account_code BETWEEN 6401 AND 6699 GROUP BY account_code, account_nameCRITICAL2025-07-28 06:02:04�
 )=�c�Y3financial_data收入科目细分规则主营业务收入(6001-6099)，其他业务收入(6101-6199)，投资收益(6301-6399)，营业外收入(6701-6799)，所有收入类科目必须使用credit_amount字段SELECT account_code, account_name, SUM(credit_amount) as 收入金额 FROM financial_data WHERE account_code BETWEEN 6001 AND 6799 GROUP BY account_code, account_nameCRITICAL2025-07-28 06:02:04�j
 )=��Y3financial_data科目编号识别规则资产类科目编号以1开头(1001-1999)，负债类以2开头(2001-2999)，所有者权益类以3开头(3001-3999)，成本类以4开头(4001-4999)，损益类以5-6开头(5001-6999)SELECT account_code, account_name FROM financial_data WHERE account_code BETWEEN 1001 AND 1999 LIMIT 5CRITICAL2025-07-28 06:02:04�y
 )1�Y�13financial_data科目编号识别1xxx=资产类，2xxx=负债类，3xxx=所有者权益类，60xx=收入类，64xx/66xx=成本费用类SELECT account_code, account_name FROM financial_data WHERE account_code LIKE "1%"HIGH2025-07-28 04:19:39�:	 )1�!m3financial_data数据类型转换balance字段为TEXT类型，需要使用CAST(balance AS REAL)进行转换SELECT CAST(balance AS REAL) FROM financial_dataHIGH2025-07-28 04:19:39�
 )O��W3financial_data科目分类与金额字段对应成本费用类科目必须使用debit_amount或debit_cumulative字段SELECT SUM(debit_amount) FROM financial_data WHERE account_code LIKE "64%" OR account_code LIKE "66%"CRITICAL2025-07-28 04:19:39�a
 )O��#3financial_data科目分类与金额字段对应收入类科目必须使用credit_amount或credit_cumulative字段SELECT SUM(credit_amount) FROM financial_data WHERE account_code LIKE "60%"CRITICAL2025-07-28 04:19:39�`
 )O��13financial_data科目分类与金额字段对应资产负债类科目必须使用balance字段进行汇总SELECT SUM(CAST(balance AS REAL)) FROM financial_data WHERE account_code LIKE "1%"CRITICAL2025-07-28 04:  ��  ��    H]�6�o��}�{��-
   � ���                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                 )query_patterns3field_relationships)business_rules  ��    H]�6�o�b{�z�
   
� D~
�
6d
�                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  �Y
 )%'%[�Q3financial_datadebit_amountcredit_amount借贷关系借贷双方记录业务的两个方面SELECT debit_amount, credit_amount FROM financial_data WHERE debit_amount > 0 OR credit_amount > 02025-07-28 06:02:04�O
 )'/%[�33financial_datacredit_amountcredit_cumulative累计关系当期贷方金额累计为贷方累计SELECT credit_amount, credit_cumulative FROM financial_data WHERE credit_amount > 02025-07-28 06:02:04�J
 )%-%[�-3financial_datadebit_amountdebit_cumulative累计关系当期借方金额累计为借方累计SELECT debit_amount, debit_cumulative FROM financial_data WHERE debit_amount > 02025-07-28 06:02:04�i
 );5I�e3financial_dataaccounting_organizationaccounting_unit_name一对一组织编号对应组织名称SELECT accounting_organization, accounting_unit_name FROM financial_data WHERE accounting_organization = 1012025-07-28 06:02:04�	 )=}3financial_datayearmonth一对多年份包含多个月份SELECT year, month FROM financial_data WHERE year = ********-07-28 06:02:04�C
 )%/I�53financial_dataaccount_codeaccount_direction一对一科目编号决定借贷方向SELECT account_code, account_direction FROM financial_data WHERE account_code = ********-07-28 06:02:04�9
 )%%I�+3financial_dataaccount_codeaccount_name一对一科目编号对应科目名称SELECT account_code, account_name FROM financial_data WHERE account_code = ********-07-28 06:02:04  ��    H]�6�o��ӯ�Y|�
    R�
�	�                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        �x =[s�Eu13组织对比分析模式不同组织单位的财务数据对比组织对比,单位对比,组织绩效,单位绩效SELECT accounting_unit_name, SUM(credit_amount) as 收入, SUM(debit_amount) as 费用 FROM financial_data WHERE year = {year} GROUP BY accounting_unit_nameaccounting_unit_name,credit_amount,debit_amount,year组织对比分析MEDIUM2025-07-28 06:02:04�b =as�1W13时间对比分析模式不同时间段的财务数据对比分析同比分析,环比分析,时间对比,趋势分析SELECT year, month, SUM(credit_amount) as 当期收入 FROM financial_data WHERE account_code LIKE "60%" GROUP BY year, month ORDER BY year, monthyear,month,credit_amount,account_code时间序列分析MEDIUM2025-07-28 06:02:04� =gy�}e13资产负债分析模式分析企业资产负债情况的查询模式资产分析,负债分析,余额分析,资产负债表SELECT account_name, SUM(CAST(balance AS REAL)) as 余额 FROM financial_data WHERE account_code < 4000 AND year = {year} AND month = {month} GROUP BY account_name ORDER BY 余额 DESCaccount_code,balance,account_name,year,month资产负债分析MEDIUM2025-07-28 06:02:04�= 1[s�a%3费用分析模式分析企业费用支出的查询模式查询费用,费用分析,成本分析,支出分析SELECT accounting_unit_name, SUM(debit_amount) as 费用金额 FROM financial_data WHERE account_code LIKE "64%" OR account_code LIKE "66%" AND year = {year} AND month = {month} GROUP BY accounting_unit_name ORDER BY 费用金额 DESCaccount_code,debit_amount,year,month,accounting_unit_name费用分析EASY2025-07-28 06:02:04�+ 1[�-�%3收入分析模式分析企业收入情况的查询模式查询收入,收入分析,营业收入,主营业务收入SELECT accounting_unit_name, SUM(credit_amount) as 收入金额 FROM financial_data WHERE account_code LIKE "60%" AND year = {year} AND month = {month} GROUP BY accounting_unit_name ORDER BY 收入金额 DESCaccount_code,credit_amount,year,month,accounting_unit_name收入分析EASY2025-07-28 06:02:04  ��    H]�6�o��yO��� �
   � 9
7z~
r�m���                                                                                                                                                                                                                                                                                                                                                                                                �E
 )1�i�93financial_data组织筛选规则按组织查询时，可使用accounting_organization(数字编号)或accounting_unit_name(名称)进行筛选SELECT accounting_organization, accounting_unit_name, COUNT(*) as 记录数 FROM financial_data GROUP BY accounting_organization, accounting_unit_nameHIGH2025-07-28 06:02:04�
 )1�I�q3financial_data时间筛选规则查询特定时间段时，必须同时使用year和month字段进行筛选，月份范围1-12SELECT year, month, COUNT(*) as 记录数 FROM financial_data WHERE year = 2024 AND month = 9 GROUP BY year, monthHIGH2025-07-28 06:02:04�n

 )1��i3financial_data负债科目规则流动负债(2001-2199)，非流动负债(2201-2999)，所有负债类科目必须使用balance字段，且需要CAST转换SELECT account_code, account_name, SUM(CAST(balance AS REAL)) as 负债余额 FROM financial_data WHERE account_code BETWEEN 2001 AND 2999 GROUP BY account_code, account_nameCRITICAL2025-07-28 06:02:04�n	
 )1��i3financial_data资产科目规则流动资产(1001-1199)，非流动资产(1201-1999)，所有资产类科目必须使用balance字段，且需要CAST转换SELECT account_code, account_name, SUM(CAST(balance AS REAL)) as 资产余额 FROM financial_data WHERE account_code BETWEEN 1001 AND 1999 GROUP BY account_code, account_nameCRITICAL2025-07-28 06:02:04�
 )=�O�W3financial_data费用科目细分规则主营业务成本(6401-6499)，销售费用(6601-6699)，管理费用(6602-6699)，财务费用(6603-6699)，所有费用类科目必须使用debit_amount字段SELECT account_code, account_name, SUM(debit_amount) as 费用金额 FROM financial_data WHERE account_code BETWEEN 6401 AND 6699 GROUP BY account_code, account_nameCRITICAL2025-07-28 06:02:04�
 )=�c�Y3financial_data收入科目细分规则主营业务收入(6001-6099)，其他业务收入(6101-6199)，投资收益(6301-6399)，营业外收入(6701-6799)，所有收入类科目必须使用credit_amount字段SELECT account_code, account_name, SUM(credit_amount) as 收入金额 FROM financial_data WHERE account_code BETWEEN 6001 AND 6799 GROUP BY account_code, account_nameCRITICAL2025-07-28 06:02:04�j
 )=��Y3financial_data科目编号识别规则资产类科目编号以1开头(1001-1999)，负债类以2开头(2001-2999)，所有者权益类以3开头(3001-3999)，成本类以4开头(4001-4999)，损益类以5-6开头(5001-6999)SELECT account_code, account_name FROM financial_data WHERE account_code BETWEEN 1001 AND 1999 LIMIT 5CRITICAL2025-07-28 06:02:04�y
 )1�Y�13financial_data科目编号识别1xxx=资产类，2xxx=负债类，3xxx=所有者权益类，60xx=收入类，64xx/66xx=成本费用类SELECT account_code, account_name FROM financial_data WHERE account_code LIKE "1%"HIGH2025-07-28 04:19:39�:	 )1�!m3financial_data数据类型转换balance字段为TEXT类型，需要使用CAST(balance AS REAL)进行转换SELECT CAST(balance AS REAL) FROM financial_dataHIGH2025-07-28 04:19:39�
 )O��W3financial_data科目分类与金额字段对应成本费用类科目必须使用debit_amount或debit_cumulative字段SELECT SUM(debit_amount) FROM financial_data WHERE account_code LIKE "64%" OR account_code LIKE "66%"CRITICAL2025-07-28 04:19:39�a
 )O��#3financial_data科目分类与金额字段对应收入类科目必须使用credit_amount或credit_cumulative字段SELECT SUM(credit_amount) FROM financial_data WHERE account_code LIKE "60%"CRITICAL2025-07-28 04:19:39�`
 )O��13financial_data科目分类与金额字段对应资产负债类科目必须使用balance字段进行汇总SELECT SUM(CAST(balance AS REAL)) FROM financial_data WHERE account_code LIKE "1%"CRITICAL2025-07-28 04:19:39  �� p/H]�6�o���Uj.y
   	� e
?;	�                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        �D
 )=�_�13financial_data借贷平衡验证规则在会计期间内，借方发生额总和应等于贷方发生额总和，可用于数据完整性验证SELECT SUM(debit_amount) as 借方总额, SUM(credit_amount) as 贷方总额, SUM(debit_amount) - SUM(credit_amount) as 差额 FROM financial_dataMEDIUM2025-07-28 06:02:04�7
 )=�i�3financial_data财务报表编制规则编制资产负债表时使用balance字段，编制利润表时收入用credit_amount，费用用debit_amountSELECT "资产负债表示例" as 报表类型, SUM(CAST(balance AS REAL)) as 金额 FROM financial_data WHERE account_code < 4000HIGH2025-07-28 06:02:04�
 )1�]�93financial_data空值处理规则在进行汇总计算时，应考虑NULL值和空字符串的处理，使用COALESCE或WHERE条件过滤SELECT COUNT(*) as 总记录, COUNT(balance) as 非空余额记录 FROM financial_dataMEDIUM2025-07-28 06:02:04�#
 )=�]�u3financial_data数据类型处理规则balance字段为TEXT类型，在进行数值计算时必须使用CAST(balance AS REAL)进行类型转换SELECT CAST(balance AS REAL) as 数值余额 FROM financial_data WHERE balance IS NOT NULL AND balance != "" LIMIT 5HIGH2025-07-28 06:02:04�
 )1�7�	3financial_data金额汇总规则进行金额汇总时，必须根据科目类型选择正确的金额字段：资产负债用balance，收入用credit_amount，费用用debit_amountSELECT CASE WHEN account_code < 2000 THEN "资产" WHEN account_code < 3000 THEN "负债" WHEN account_code >= 6000 THEN "损益" END as 科目类型, COUNT(*) FROM financial_data GROUP BY 1CRITICAL2025-07-28 06:02:04       H]�6�o��0��f^KSQLite format 3   @      p/  ��  �   7                                                  .n�   �   �����< �� �                  �l
33�{tablecolumn_descriptionscolumn_descriptions ��CREATE TABLE column_descriptions (
                table_name TEXT NOT NULL,
                column_name TEXT NOT NULL,
                chinese_name TEXT,
                description TEXT,
                data_type TEXT,
                business_rules TEXT,
                ai_understanding_points TEXT, field_category TEXT DEFAULT '', usage_scenarios TEXT DEFAULT '', common_values TEXT DEFAULT '', related_fields TEXT DEFAULT '', calculation_rules TEXT DEFAULT '', ai_prompt_hints TEXT DEFAULT '',
                PRIMARY KEY (table_name, column_name)
            )�*))�tablebusiness_rulesbusiness_rules ��CREATE TABLE business_rules (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                table_name TEXT NOT NULL,
                rule_category TEXT NOT NULL,
                rule_description TEXT NOT NULL,
                sql_example TEXT,
                importance_level TEXT DEFAULT 'HIGH',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )GY3 indexsqlite_autoindex_column_descriptions_1column_descriptions �Ă?11�)tabletable_descriptionstable_descriptionsCREATE TABLE table_descriptions (
                table_name TEXT PRIMARY KEY,
                description TEXT NOT NULL,
                business_purpose TEXT,
                data_scale TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )EW1 indexsqlite_autoindex_table_descriptions_1table_descriptions �p
KK�[viewfinancial_data_columns_metadatafinancial_data_columns_metadataCREATE VIEW financial_data_columns_metadata AS
            SELECT 
                column_name,
                chinese_name,
                description,
                data_type,
                business_rules,
                ai_understanding_points
            FROM column_descriptions
            WHERE table_name = 'financial_data'
            ORDER BY 
                CASE 
                    WHEN column_name IN ('year', 'month') THEN 1
                    WHEN column_name LIKE 'accounting_%' THEN 2
                    WHEN column_name LIKE 'account_%' THEN 3
                    WHEN column_name LIKE '%amount%' OR column_name LIKE '%cumulative%' OR column_name = 'balance' THEN 4
                    WHEN column_name LIKE 'project_%' THEN 5
                    WHEN column_name LIKE 'bank_%' OR column_name LIKE 'financial_institution_%' THEN 6
                    ELSE 7
                END,
                column_name�S	EE�-viewfinancial_data_with_metadatafinancial_data_with_metadataCREATE VIEW financial_data_with_metadata AS
            SELECT 
                'financial_data' as table_name,
                td.description as table_description,
                td.business_purpose,
                td.data_scale,
                (SELECT COUNT(*) FROM financial_data) as current_record_count
            FROM table_descriptions td
            WHERE td.table_name = 'financial_data'R++Ytablesqlite_sequencesqlite_sequence ��CREATE TABLE sqlite_sequence(name,seq)�]))�qtablefinancial_datafinancial_data �CREATE TABLE "financial_data" ("year" INTEGER, "month" INTEGER, "accounting_organization" INTEGER, "accounting_unit_name" TEXT, "account_code" INTEGER, "account_full_name" TEXT, "account_name" TEXT, "opening_debit_amount" REAL, "opening_credit_amount" INTEGER, "account_direction" TEXT, "project_id" TEXT, "project_code" TEXT, "project_name" TEXT, "market_nature_id" TEXT, "tax_rate_id" TEXT, "tax_rate_name" TEXT, "business_format_id" TEXT, "financial_product_id" TEXT, "long_term_deferred_project_id" TEXT, "property_unit_id" TEXT, "cash_flow_project_id" TEXT, "municipal_enterprise_unit_id" TEXT, "bank_account_id" TEXT, "financial_institution_id" INTEGER, "bank_routing_number" INTEGER, "bank_name" TEXT, "debit_amount" REAL, "debit_cumulative" REAL, "credit_amount" REAL, "credit_cumulative" REAL, "balance"   ��
  ��    H]�6�o��F[���Q  ��  F  ��  �  �  �  �  �  �  �  �  �  �  �
  �  �  �
  �	  �  �  �  �  �  �  �  �  �   ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  �   �  �  �  �  �  �  �  �  �	  �
  �  �  �
  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �   �!  �"  �#  �$  �%  �&  �'  �(  �)  �*  �+  �,  �-  �.  �/  �0  �1  �2  �3  �4  �5  �6  �7  �8  �9  �:  �;  �<  �=  �>  �?  �@  �A  �B  �C  �D  �E  �F  �G  �H  �I  �J  �K  �L  �M  �N  �O  �P  �Q  �R  �S  �T  �U  �V  �W  �X  �Y  �Z  �[  �\  �]  �^  �_  �`  �a  �b  �c  �d  �e  �f  �g  �h  �i  �j  �k  �l  �n  �o  �p  �q  �r  �s  �t  �u  �v  �w  �x  �y  �z  �{  �|  �}  �~  �  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  �m  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��   行存款借       79035880-87f3-4a3b-ae13-6ab820525793 49777ac1-2845-8af5-1789-54cb4a72c850 G�
0� G�
0�招商银行股份有限公司深圳分行票据业务部�-4480.00000000�B��!�;%%


UU+�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       f5e52502-a987-41cd-9628-9e72ce7e508f 49777ac1-2845-8af5-1789-54cb4a72c850 G�
0� G�
0�招商银行股份有限公司深圳分行票据业务部p�-28879.00000000�D��!�;%%


UU#�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       7604f734-3f8a-4767-a39e-08183d22cb35 49777ac1-2845-8af5-1789-54cb4a72c850 G�
0� G�
0�招商银行股份有限公司深圳分行票据业务部@Iٙ����51.70000000�I��!�;%%


UU-�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       a4be09b9-c44f-4194-a848-2d1aa0d1911c 49777ac1-2845-8af5-1789-54cb4a72c850 G�
0� G�
0�招商银行股份有限公司深圳分行票据业务部A������-340922.95000000�,��!�;%%


UUO-�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       f5e52502-a987-41cd-9628-9e72ce7e508f 49777ac1-2845-8af5-1789-54cb4a72c850 G�
. G�
.招商银行深圳分行营业部T�-152735.00000000�/��!�;%%


UUO/�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       79035880-87f3-4a3b-ae13-6ab820525793 49777ac1-2845-8af5-1789-54cb4a72c850 G�
. G�
.招商银行深圳分行营业部 XX�-1325136.00000000�0��!�;%%


UUO)�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       5ec52f35-fa80-4795-9f3d-df0dd5006c6a 49777ac1-2845-8af5-1789-54cb4a72c850 G�
. G�
.招商银行深圳分行营业部@�Y�\)-1789.49000000�8��!�;%%


UUO+�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       3465b495-bfa8-41bd-99fe-49e8efb61e26 49777ac1-2845-8af5-1789-54cb4a72c850 G�
. G�
.招商银行深圳分行营业部A(۞z�H�ATz�G�814577.70000000�-��!�;%%


UUO%�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       7604f734-3f8a-4767-a39e-08183d22cb35 49777ac1-2845-8af5-1789-54cb4a72c850 G�
. G�
.招商银行深圳分行营业部@nz�G�240.19000000  ��    H]�6�o��R<7�'��
   � �����                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            1data_quality_rules3ai_prompt_templates)query_patterns3field_relationships)business_rules  ��    H]�6�o�;Q���,�x
   B 8�
���B                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              �: )'%C�=3financial_datacredit_amount逻辑检查贷方金额应为非负数SELECT COUNT(*) FROM financial_data WHERE credit_amount < 0贷方金额出现负值WARNING2025-07-28 06:02:22�8 )%%C�=3financial_datadebit_amount逻辑检查借方金额应为非负数SELECT COUNT(*) FROM financial_data WHERE debit_amount < 0借方金额出现负值WARNING2025-07-28 06:02:22� )%Q�o]3financial_databalance类型检查balance字段应可转换为数值SELECT COUNT(*) FROM financial_data WHERE balance IS NOT NULL AND balance != "" AND CAST(balance AS REAL) IS NULLbalance字段包含无法转换的数据ERROR2025-07-28 06:02:22�E )%%E�C3financial_dataaccount_code格式检查科目编号应为4位数字SELECT COUNT(*) FROM financial_data WHERE LENGTH(account_code) != 4科目编号格式不正确WARNING2025-07-28 06:02:22�1 )%9�=3financial_datamonth范围检查月份应在1-12之间SELECT COUNT(*) FROM financial_data WHERE month < 1 OR month > 12月份超出有效范围ERROR2025-07-28 06:02:22�E )%Y�=3financial_datayear范围检查年份应在合理范围内(2020-2030)SELECT COUNT(*) FROM financial_data WHERE year < 2020 OR year > 2030年份超出合理范围WARNING2025-07-28 06:02:22  ��    H]�6�o��n^�   �   ��� 1                                 �*
 -�S1	3error_preventionWARNING🚨 **常见错误预防指南**：

❌ **严禁的错误操作**：
1. 收入查询使用balance字段 → 结果完全错误
2. 费用查询使用credit_amount字段 → 结果完全错误  
3. 资产查询使用debit_amount字段 → 结果完全错误
4. balance字段不进行类型转换 → 无法正确计算
5. 忽略科目编号规律 → 科目分类错误

✅ **正确的操作示例**：
```sql
-- ✅ 正确：收入查询
SELECT SUM(credit_amount) FROM financial_data WHERE account_code LIKE '60%';

-- ❌ 错误：收入查询  
SELECT SUM(CAST(balance AS REAL)) FROM financial_data WHERE account_name LIKE '%收入%';

-- ✅ 正确：资产查询
SELECT SUM(CAST(balance AS REAL)) FROM financial_data WHERE account_code LIKE '1%';

-- ❌ 错误：资产查询
SELECT SUM(debit_amount) FROM financial_data WHERE account_name LIKE '%资产%';
```错误预防指南	2025-07-28 06:02:22�E
 1�1	3query_constructionGUIDANCE🔧 **SQL查询构建指导**：

📝 **标准查询流程**：
1. **理解业务需求** → 识别查询目的（收入分析/费用分析/余额查询等）
2. **确定科目范围** → 根据科目编号确定查询范围
3. **选择正确字段** → 根据科目类型选择对应的金额字段
4. **添加筛选条件** → 时间、组织、项目等维度筛选
5. **设计聚合逻辑** → GROUP BY和聚合函数的使用
6. **优化查询性能** → 合理使用索引和筛选条件

💡 **常用查询模式**：
- 收入分析：WHERE account_code LIKE '60%' → SUM(credit_amount)
- 费用分析：WHERE account_code LIKE '64%' OR account_code LIKE '66%' → SUM(debit_amount)  
- 资产分析：WHERE account_code LIKE '1%' → SUM(CAST(balance AS REAL))
- 时间筛选：WHERE year = 2024 AND month = 9
- 组织筛选：WHERE accounting_unit_name LIKE '%关键词%' 查询构建指导2025-07-28 06:02:22�O
 +�#1	3financial_rulesRULES📊 **财务数据查询核心规则** (必须严格遵守)：

🔴 **科目分类与字段对应规则**：
┌─────────────────┬──────────────────┬─────────────────┐
│   科目类型      │    科目编号范围   │   使用字段      │
├─────────────────┼──────────────────┼─────────────────┤
│ 资产类科目      │ 1001-1999       │ balance         │
│ 负债类科目      │ 2001-2999       │ balance         │
│ 所有者权益类    │ 3001-3999       │ balance         │
│ 收入类科目      │ 6001-6199       │ credit_amount   │
│ 费用类科目      │ 6401-6699       │ debit_amount    │
└─────────────────┴──────────────────┴─────────────────┘

⚠️ **关键注意事项**：
- balance字段为TEXT类型，必须使用 CAST(balance AS REAL) 转换
- 错误的字段选择将导致完全错误的财务分析结果
- 根据科目编号自动判断科目类型，选择正确的金额字段财务规则强化
2025-07-28 06:02:22�
 #�1	3system_roleSYSTEM你是一名资深的财务数据分析专家和SQL开发专家，具有以下专业能力：

🎯 **核心专长**：
1. **财务专业知识**：深度理解会计准则、财务报表结构、科目分类体系
2. **SQL技术能力**：精通复杂查询、数据聚合、多表关联、性能优化
3. **业务理解能力**：准确理解用户的财务分析需求和业务场景
4. **数据质量意识**：关注数据准确性、完整性和一致性

🏗️ **工作原则**：
- 严格遵循财务业务规则，确保查询结果的准确性
- 优先考虑数据的业务含义，而非单纯的技术实现
- 提供清晰的查询逻辑说明和结果解释
- 注重查询性能和可维护性系统角色定义
2025-07-28 06:  ��  ��    H]�6�o�S���n�Q~
    1 ��� 1                                 �*
 -�S1	3error_preventionWARNING🚨 **常见错误预防指南**：

❌ **严禁的错误操作**：
1. 收入查询使用balance字段 → 结果完全错误
2. 费用查询使用credit_amount字段 → 结果完全错误  
3. 资产查询使用debit_amount字段 → 结果完全错误
4. balance字段不进行类型转换 → 无法正确计算
5. 忽略科目编号规律 → 科目分类错误

✅ **正确的操作示例**：
```sql
-- ✅ 正确：收入查询
SELECT SUM(credit_amount) FROM financial_data WHERE account_code LIKE '60%';

-- ❌ 错误：收入查询  
SELECT SUM(CAST(balance AS REAL)) FROM financial_data WHERE account_name LIKE '%收入%';

-- ✅ 正确：资产查询
SELECT SUM(CAST(balance AS REAL)) FROM financial_data WHERE account_code LIKE '1%';

-- ❌ 错误：资产查询
SELECT SUM(debit_amount) FROM financial_data WHERE account_name LIKE '%资产%';
```错误预防指南	2025-07-28 06:02:22�E
 1�1	3query_constructionGUIDANCE🔧 **SQL查询构建指导**：

📝 **标准查询流程**：
1. **理解业务需求** → 识别查询目的（收入分析/费用分析/余额查询等）
2. **确定科目范围** → 根据科目编号确定查询范围
3. **选择正确字段** → 根据科目类型选择对应的金额字段
4. **添加筛选条件** → 时间、组织、项目等维度筛选
5. **设计聚合逻辑** → GROUP BY和聚合函数的使用
6. **优化查询性能** → 合理使用索引和筛选条件

💡 **常用查询模式**：
- 收入分析：WHERE account_code LIKE '60%' → SUM(credit_amount)
- 费用分析：WHERE account_code LIKE '64%' OR account_code LIKE '66%' → SUM(debit_amount)  
- 资产分析：WHERE account_code LIKE '1%' → SUM(CAST(balance AS REAL))
- 时间筛选：WHERE year = 2024 AND month = 9
- 组织筛选：WHERE accounting_unit_name LIKE '%关键词%' 查询构建指导2025-07-28 06:02:22�O
 +�#1	3financial_rulesRULES📊 **财务数据查询核心规则** (必须严格遵守)：

🔴 **科目分类与字段对应规则**：
┌─────────────────┬──────────────────┬─────────────────┐
│   科目类型      │    科目编号范围   │   使用字段      │
├─────────────────┼──────────────────┼─────────────────┤
│ 资产类科目      │ 1001-1999       │ balance         │
│ 负债类科目      │ 2001-2999       │ balance         │
│ 所有者权益类    │ 3001-3999       │ balance         │
│ 收入类科目      │ 6001-6199       │ credit_amount   │
│ 费用类科目      │ 6401-6699       │ debit_amount    │
└─────────────────┴──────────────────┴─────────────────┘

⚠️ **关键注意事项**：
- balance字段为TEXT类型，必须使用 CAST(balance AS REAL) 转换
- 错误的字段选择将导致完全错误的财务分析结果
- 根据科目编号自动判断科目类型，选择正确的金额字段财务规则强化
2025-07-28 06:02:22�
 #�1	3system_roleSYSTEM你是一名资深的财务数据分析专家和SQL开发专家，具有以下专业能力：

🎯 **核心专长**：
1. **财务专业知识**：深度理解会计准则、财务报表结构、科目分类体系
2. **SQL技术能力**：精通复杂查询、数据聚合、多表关联、性能优化
3. **业务理解能力**：准确理解用户的财务分析需求和业务场景
4. **数据质量意识**：关注数据准确性、完整性和一致性

🏗️ **工作原则**：
- 严格遵循财务业务规则，确保查询结果的准确性
- 优先考虑数据的业务含义，而非单纯的技术实现
- 提供清晰的查询逻辑说明和结果解释
- 注重查询性能和可维护性系统角色定义
2025-07-28 06:02:22  �� p/H]�6�o��!�C&֗"
   
% �
%                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         �
 %!�?1	3data_qualityVALIDATION🔍 **数据质量检查指导**：

📋 **查询前检查**：
1. 确认时间范围的合理性（年份、月份）
2. 验证组织单位名称的准确性
3. 检查科目编号的有效性

📊 **查询后验证**：
1. 检查结果数量的合理性
2. 验证金额数据的逻辑性（正负值）
3. 确认汇总数据的平衡性

⚖️ **借贷平衡验证**：
```sql
-- 验证借贷平衡
SELECT 
    SUM(debit_amount) as 借方总额,
    SUM(credit_amount) as 贷方总额,
    SUM(debit_amount) - SUM(credit_amount) as 差额
FROM financial_data 
WHERE year = 2024 AND month = 9;
```数据质量检查2025-07-28 06:02:22�6
 1�g1	3business_scenariosCONTEXT🏢 **常见业务分析场景**：

📈 **收入分析场景**：
- 月度收入统计、年度收入对比、收入结构分析
- 主营业务收入 vs 其他业务收入分析
- 各组织单位收入贡献度分析

💰 **费用分析场景**：
- 成本费用结构分析、费用控制效果评估
- 管理费用、销售费用、财务费用分类统计
- 费用率分析（费用/收入比例）

🏦 **资产负债分析场景**：
- 资产结构分析、负债水平评估
- 流动资产 vs 非流动资产分析
- 资产负债率、流动比率等财务指标计算

📊 **综合分析场景**：
- 利润表编制（收入-费用）
- 资产负债表编制（资产=负债+所有者权益）
- 现金流量分析、财务比率分析业务场景指导2025-07-28 06:02:22