"""
Redis缓存服务
提供分布式缓存支持
"""
import json
import logging
import pickle
from typing import Any, Optional, Dict
import redis.asyncio as redis
from redis.exceptions import ConnectionError, TimeoutError

from app.core.config import settings

logger = logging.getLogger(__name__)


class RedisCacheService:
    """Redis缓存服务"""
    
    def __init__(self):
        self.redis_client = None
        self.connection_pool = None
        self._initialized = False
        
        # 缓存配置
        self.default_ttl = 3600  # 1小时
        self.schema_ttl = 14400   # 4小时
        self.qa_ttl = 7200       # 2小时
        
        # 键前缀
        self.key_prefix = "chatdb:"
    
    async def initialize(self):
        """初始化Redis连接"""
        if self._initialized:
            return
        
        try:
            # 创建连接池
            self.connection_pool = redis.ConnectionPool(
                host=getattr(settings, 'REDIS_HOST', 'localhost'),
                port=getattr(settings, 'REDIS_PORT', 6379),
                db=getattr(settings, 'REDIS_DB', 0),
                password=getattr(settings, 'REDIS_PASSWORD', None),
                max_connections=20,
                retry_on_timeout=True,
                socket_timeout=5,
                socket_connect_timeout=5
            )
            
            # 创建Redis客户端
            self.redis_client = redis.Redis(connection_pool=self.connection_pool)
            
            # 测试连接
            await self.redis_client.ping()
            
            self._initialized = True
            logger.info("Redis缓存服务初始化成功")
            
        except Exception as e:
            logger.warning(f"Redis连接失败，将使用内存缓存: {str(e)}")
            self._initialized = False
    
    def _make_key(self, key: str) -> str:
        """生成完整的缓存键"""
        return f"{self.key_prefix}{key}"
    
    async def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        if not self._initialized:
            return None
        
        try:
            full_key = self._make_key(key)
            data = await self.redis_client.get(full_key)
            
            if data is None:
                return None
            
            # 尝试反序列化
            try:
                return pickle.loads(data)
            except:
                # 如果pickle失败，尝试JSON
                return json.loads(data.decode('utf-8'))
                
        except (ConnectionError, TimeoutError) as e:
            logger.warning(f"Redis获取失败: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"缓存获取错误: {str(e)}")
            return None
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """设置缓存值"""
        if not self._initialized:
            return False
        
        try:
            full_key = self._make_key(key)
            ttl = ttl or self.default_ttl
            
            # 序列化数据
            try:
                data = pickle.dumps(value)
            except:
                # 如果pickle失败，使用JSON
                data = json.dumps(value, default=str).encode('utf-8')
            
            await self.redis_client.setex(full_key, ttl, data)
            return True
            
        except (ConnectionError, TimeoutError) as e:
            logger.warning(f"Redis设置失败: {str(e)}")
            return False
        except Exception as e:
            logger.error(f"缓存设置错误: {str(e)}")
            return False
    
    async def delete(self, key: str) -> bool:
        """删除缓存值"""
        if not self._initialized:
            return False
        
        try:
            full_key = self._make_key(key)
            result = await self.redis_client.delete(full_key)
            return result > 0
            
        except Exception as e:
            logger.error(f"缓存删除错误: {str(e)}")
            return False
    
    async def delete_pattern(self, pattern: str) -> int:
        """根据模式删除缓存"""
        if not self._initialized:
            return 0
        
        try:
            full_pattern = self._make_key(pattern)
            keys = await self.redis_client.keys(full_pattern)
            
            if keys:
                deleted = await self.redis_client.delete(*keys)
                logger.info(f"根据模式 '{pattern}' 删除了 {deleted} 个缓存项")
                return deleted
            
            return 0
            
        except Exception as e:
            logger.error(f"模式删除错误: {str(e)}")
            return 0
    
    async def exists(self, key: str) -> bool:
        """检查键是否存在"""
        if not self._initialized:
            return False
        
        try:
            full_key = self._make_key(key)
            return await self.redis_client.exists(full_key) > 0
        except Exception as e:
            logger.error(f"检查键存在性错误: {str(e)}")
            return False
    
    async def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        if not self._initialized:
            return {'status': 'disconnected'}
        
        try:
            info = await self.redis_client.info()
            return {
                'status': 'connected',
                'used_memory': info.get('used_memory_human', 'N/A'),
                'connected_clients': info.get('connected_clients', 0),
                'total_commands_processed': info.get('total_commands_processed', 0),
                'keyspace_hits': info.get('keyspace_hits', 0),
                'keyspace_misses': info.get('keyspace_misses', 0)
            }
        except Exception as e:
            logger.error(f"获取Redis统计信息错误: {str(e)}")
            return {'status': 'error', 'error': str(e)}
    
    async def close(self):
        """关闭Redis连接"""
        if self.redis_client:
            await self.redis_client.close()
        if self.connection_pool:
            await self.connection_pool.disconnect()
        
        self._initialized = False
        logger.info("Redis缓存服务已关闭")


# 全局Redis缓存实例
redis_cache = RedisCacheService()


class HybridCacheService:
    """混合缓存服务（内存+Redis）"""
    
    def __init__(self):
        from app.services.cache_service import cache_service
        self.memory_cache = cache_service
        self.redis_cache = redis_cache
    
    async def get(self, key: str) -> Optional[Any]:
        """获取缓存值（先内存后Redis）"""
        # 先尝试内存缓存
        result = self.memory_cache.get(key)
        if result is not None:
            return result
        
        # 再尝试Redis缓存
        result = await self.redis_cache.get(key)
        if result is not None:
            # 回写到内存缓存
            self.memory_cache.set(key, result, ttl=300)  # 5分钟内存缓存
        
        return result
    
    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """设置缓存值（同时写入内存和Redis）"""
        # 写入内存缓存
        memory_ttl = min(ttl or 3600, 1800)  # 内存缓存最多30分钟
        self.memory_cache.set(key, value, memory_ttl)
        
        # 写入Redis缓存
        return await self.redis_cache.set(key, value, ttl)
    
    async def delete(self, key: str) -> bool:
        """删除缓存值"""
        # 删除内存缓存
        self.memory_cache.delete(key)
        
        # 删除Redis缓存
        return await self.redis_cache.delete(key)


# 全局混合缓存实例
hybrid_cache = HybridCacheService()
