# Import all the models, so that Base has them before being
# imported by Alembic
from app.db.base_class import Base  # noqa
from app.models.db_connection import DBConnection  # noqa
from app.models.schema_table import SchemaTable  # noqa
from app.models.schema_column import SchemaColumn  # noqa
from app.models.schema_relationship import SchemaRelationship  # noqa
from app.models.value_mapping import ValueMapping  # noqa
from app.models.chat_history import ChatSession, ChatMessage, ChatHistorySnapshot  # noqa

from app.models.metadata_models import FieldRelationship, QueryPattern, DataQualityRule, AIPromptTemplate, EnhancedColumnDescription  # noqa
