"""
元数据相关的ORM模型定义
"""

from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean
from sqlalchemy.sql import func
from app.db.base_class import Base


class FieldRelationship(Base):
    """字段关系模型"""
    __tablename__ = "field_relationships"
    
    id = Column(Integer, primary_key=True, index=True)
    table_name = Column(String(255), nullable=False)
    primary_field = Column(String(255), nullable=False)
    related_field = Column(String(255), nullable=False)
    relationship_type = Column(String(100), nullable=False)
    relationship_description = Column(Text, nullable=True)
    usage_example = Column(Text, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())


class QueryPattern(Base):
    """查询模式模型"""
    __tablename__ = "query_patterns"
    
    id = Column(Integer, primary_key=True, index=True)
    pattern_name = Column(String(255), nullable=False)
    pattern_description = Column(Text, nullable=False)
    natural_language_examples = Column(Text, nullable=False)
    sql_template = Column(Text, nullable=False)
    required_fields = Column(Text, nullable=False)
    business_scenario = Column(String(255), nullable=True)
    difficulty_level = Column(String(50), default='MEDIUM')
    created_at = Column(DateTime(timezone=True), server_default=func.now())


class DataQualityRule(Base):
    """数据质量规则模型"""
    __tablename__ = "data_quality_rules"
    
    id = Column(Integer, primary_key=True, index=True)
    table_name = Column(String(255), nullable=False)
    field_name = Column(String(255), nullable=False)
    rule_type = Column(String(100), nullable=False)
    rule_description = Column(Text, nullable=False)
    validation_sql = Column(Text, nullable=True)
    error_message = Column(Text, nullable=True)
    severity_level = Column(String(50), default='WARNING')
    created_at = Column(DateTime(timezone=True), server_default=func.now())


class AIPromptTemplate(Base):
    """AI提示模板模型"""
    __tablename__ = "ai_prompt_templates"
    
    id = Column(Integer, primary_key=True, index=True)
    template_name = Column(String(255), nullable=False)
    template_type = Column(String(100), nullable=False)
    template_content = Column(Text, nullable=False)
    usage_scenario = Column(String(255), nullable=True)
    priority_level = Column(Integer, default=5)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())


class EnhancedColumnDescription(Base):
    """增强的字段描述模型（用于ORM查询）"""
    __tablename__ = "column_descriptions"
    
    table_name = Column(String(255), primary_key=True)
    column_name = Column(String(255), primary_key=True)
    chinese_name = Column(Text, nullable=True)
    description = Column(Text, nullable=True)
    data_type = Column(Text, nullable=True)
    business_rules = Column(Text, nullable=True)
    ai_understanding_points = Column(Text, nullable=True)
    
    # 新增的增强字段
    field_category = Column(Text, default='')
    usage_scenarios = Column(Text, default='')
    common_values = Column(Text, default='')
    related_fields = Column(Text, default='')
    calculation_rules = Column(Text, default='')
    ai_prompt_hints = Column(Text, default='')
