# Financial_Data 表描述添加完成报告

## 概述

已成功为 `main.financial_data` 表添加了完整的描述信息，包括表级别描述、字段描述和关键业务规则。这些描述信息将帮助AI模型更好地理解财务数据的结构和含义。

**完成时间**: 2025年6月26日 09:42:39  
**状态**: ✅ 成功完成  
**数据库**: fin_data.db  
**表名**: financial_data

## 完成的工作

### 1. 创建元数据表结构

#### 1.1 表描述元数据表 (table_descriptions)
- **用途**: 存储表级别的描述信息
- **字段**: table_name, description, business_purpose, data_scale, created_at
- **记录数**: 1条记录

#### 1.2 字段描述元数据表 (column_descriptions)  
- **用途**: 存储每个字段的详细描述信息
- **字段**: table_name, column_name, chinese_name, description, data_type, business_rules, ai_understanding_points
- **记录数**: 31条记录（对应31个字段）

#### 1.3 业务规则表 (business_rules)
- **用途**: 存储关键的业务规则和使用规范
- **字段**: table_name, rule_category, rule_description, sql_example, importance_level
- **记录数**: 5条关键规则

### 2. 表级别描述信息

**表名**: financial_data  
**中文名**: 财务辅助科目余额表  
**描述**: 包含了企业财务核算的详细信息，是企业财务管理系统的核心数据表，涵盖了时间维度、组织架构、会计科目、项目管理、银行信息和金额数据等多个方面。

**业务用途**:
- 用于企业财务核算、报表编制、财务分析和决策支持
- 支持按时间、组织、项目、科目等多维度进行财务数据分析  
- 为资产负债表、利润表、现金流量表等财务报表提供基础数据

**数据规模**: 723,333 行记录，31 个字段，包含整数、实数、文本等多种数据类型

### 3. 字段分类描述

#### 3.1 时间维度字段 (2个)
- `year` (年) - 财务数据所属的会计年度
- `month` (月) - 财务数据所属的会计月份

#### 3.2 组织架构字段 (2个)
- `accounting_organization` (核算组织) - 核算组织的代码标识
- `accounting_unit_name` (核算单位名称) - 核算单位的完整名称

#### 3.3 会计科目字段 (4个)
- `account_code` (科目编号) - 会计科目的数字编码
- `account_full_name` (科目全称) - 会计科目的完整名称
- `account_name` (科目名称) - 会计科目的简化名称
- `account_direction` (科目方向) - 会计科目的借贷方向属性

#### 3.4 金额字段 - 核心财务数据 (7个)
- `opening_debit_amount` (期初借方金额) - 会计期间开始时的借方余额
- `opening_credit_amount` (期初贷方金额) - 会计期间开始时的贷方余额
- `debit_amount` (借方金额) - 当期发生的借方金额
- `credit_amount` (贷方金额) - 当期发生的贷方金额
- `debit_cumulative` (借方累计) - 从年初到当期的借方累计发生额
- `credit_cumulative` (贷方累计) - 从年初到当期的贷方累计发生额
- `balance` (余额) - 科目的期末余额

#### 3.5 项目管理字段 (3个)
- `project_id` (项目ID) - 项目的唯一标识符
- `project_code` (项目编号) - 项目的编码
- `project_name` (项目名称) - 项目的名称描述

#### 3.6 业务分类字段 (9个)
- `market_nature_id` (市场性质ID)
- `tax_rate_id` (税率ID)
- `tax_rate_name` (税率名称)
- `business_format_id` (业态ID)
- `financial_product_id` (金融产品ID)
- `long_term_deferred_project_id` (长期待摊项目ID)
- `property_unit_id` (楼盘房号ID)
- `cash_flow_project_id` (现金流量项目ID)
- `municipal_enterprise_unit_id` (市属国企单位ID)

#### 3.7 银行信息字段 (4个)
- `bank_account_id` (银行账号ID)
- `financial_institution_id` (金融机构ID)
- `bank_routing_number` (联行号)
- `bank_name` (银行名称)

### 4. 关键业务规则

#### 4.1 科目分类与金额字段对应关系 (CRITICAL级别)
1. **资产负债类科目** → 必须使用 `balance` 字段
2. **收入类科目** → 必须使用 `credit_amount` 或 `credit_cumulative` 字段
3. **成本费用类科目** → 必须使用 `debit_amount` 或 `debit_cumulative` 字段

#### 4.2 数据类型转换 (HIGH级别)
- balance字段为TEXT类型，需要使用 `CAST(balance AS REAL)` 进行转换

#### 4.3 科目编号识别规则 (HIGH级别)
- 1xxx = 资产类（使用balance）
- 2xxx = 负债类（使用balance）
- 3xxx = 所有者权益类（使用balance）
- 60xx = 收入类（使用credit_amount/credit_cumulative）
- 64xx, 66xx = 成本费用类（使用debit_amount/debit_cumulative）

### 5. 创建的视图

#### 5.1 financial_data_with_metadata
- 包含表级别的元数据信息
- 显示表描述、业务用途、数据规模和当前记录数

#### 5.2 financial_data_columns_metadata  
- 包含所有字段的元数据信息
- 按字段类别排序，便于查看和理解

### 6. 提供的查询脚本

#### 6.1 financial_data_metadata_queries.sql
包含了各种查询元数据的SQL语句：
- 表级别描述查询
- 字段描述查询（按类别分组）
- 业务规则查询
- 实际业务查询示例
- 数据质量检查查询

#### 6.2 Python脚本
- `add_financial_data_descriptions.py` - 添加描述信息的脚本
- `view_financial_data_descriptions.py` - 查看描述信息的脚本
- `check_financial_data_table.py` - 检查表结构的脚本

## 使用方法

### 1. 查询表描述
```sql
SELECT * FROM table_descriptions WHERE table_name = 'financial_data';
```

### 2. 查询字段描述
```sql
SELECT * FROM column_descriptions WHERE table_name = 'financial_data';
```

### 3. 查询业务规则
```sql
SELECT * FROM business_rules WHERE table_name = 'financial_data';
```

### 4. 使用元数据视图
```sql
-- 查看表元数据
SELECT * FROM financial_data_with_metadata;

-- 查看字段元数据
SELECT * FROM financial_data_columns_metadata;
```

## AI模型使用建议

### 1. 理解字段含义
- 查询 `column_descriptions` 表获取字段的中文名和详细描述
- 重点关注 `ai_understanding_points` 字段的内容

### 2. 遵循业务规则
- 必须按照 `business_rules` 表中的规则使用不同的金额字段
- 资产负债类科目使用 `balance` 字段
- 收入类科目使用 `credit_amount` 字段  
- 成本费用类科目使用 `debit_amount` 字段

### 3. 数据类型处理
- balance字段需要使用 `CAST(balance AS REAL)` 转换
- 注意科目编号的规律识别

### 4. 查询优化
- 利用科目编号的前缀进行分类查询
- 结合时间维度进行数据分析
- 按组织架构进行数据分组

## 总结

通过添加完整的元数据描述，financial_data表现在具备了：

✅ **完整的表级别描述** - 说明表的用途和业务价值  
✅ **详细的字段描述** - 每个字段都有中文名、描述和AI理解要点  
✅ **关键业务规则** - 确保正确使用不同的金额字段  
✅ **元数据视图** - 便于查询和使用元数据信息  
✅ **查询脚本** - 提供了各种查询示例和最佳实践  

这些描述信息将显著提高AI模型对财务数据的理解能力，确保生成正确的SQL查询和财务分析结果。
