#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析SQL查询返回0的问题
"""

import sqlite3
import os

def analyze_sql_issue():
    """分析SQL查询问题"""
    
    # 数据库文件路径
    db_path = "fin_data.db"
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("=== 分析SQL查询返回0的问题 ===\n")
        
        # 1. 检查表结构
        print("1. 检查表结构:")
        cursor.execute("PRAGMA table_info(financial_data)")
        columns = cursor.fetchall()
        print("列信息:")
        for col in columns:
            print(f"  {col[1]} ({col[2]})")
        
        # 2. 检查数据总量
        print(f"\n2. 检查数据总量:")
        cursor.execute("SELECT COUNT(*) FROM financial_data")
        total_count = cursor.fetchone()[0]
        print(f"总记录数: {total_count:,}")
        
        # 3. 检查年份分布
        print(f"\n3. 检查年份分布:")
        cursor.execute("SELECT year, COUNT(*) FROM financial_data GROUP BY year ORDER BY year")
        year_data = cursor.fetchall()
        for year, count in year_data:
            print(f"  {year}年: {count:,} 条记录")
        
        # 4. 检查2024年数据
        print(f"\n4. 检查2024年数据:")
        cursor.execute("SELECT COUNT(*) FROM financial_data WHERE year = 2024")
        count_2024 = cursor.fetchone()[0]
        print(f"2024年总记录数: {count_2024:,}")
        
        if count_2024 > 0:
            # 检查2024年月份分布
            print(f"\n5. 检查2024年月份分布:")
            cursor.execute("SELECT month, COUNT(*) FROM financial_data WHERE year = 2024 GROUP BY month ORDER BY month")
            month_data = cursor.fetchall()
            for month, count in month_data:
                print(f"  {month}月: {count:,} 条记录")
            
            # 6. 检查2024年2月数据
            print(f"\n6. 检查2024年2月数据:")
            cursor.execute("SELECT COUNT(*) FROM financial_data WHERE year = 2024 AND month = 2")
            count_2024_02 = cursor.fetchone()[0]
            print(f"2024年2月总记录数: {count_2024_02:,}")
            
            if count_2024_02 > 0:
                # 7. 检查科目名称模式
                print(f"\n7. 检查科目名称模式:")
                cursor.execute("""
                    SELECT DISTINCT account_full_name 
                    FROM financial_data 
                    WHERE year = 2024 AND month = 2 
                    AND account_full_name LIKE '%主营业务收入%'
                    ORDER BY account_full_name
                """)
                account_names = cursor.fetchall()
                print(f"匹配'%主营业务收入%'的科目数量: {len(account_names)}")
                for name in account_names[:10]:  # 只显示前10个
                    print(f"  {name[0]}")
                if len(account_names) > 10:
                    print(f"  ... 还有 {len(account_names) - 10} 个科目")
                
                # 8. 检查opening_credit_amount字段
                print(f"\n8. 检查opening_credit_amount字段:")
                cursor.execute("""
                    SELECT 
                        COUNT(*) as total_records,
                        COUNT(CASE WHEN opening_credit_amount IS NOT NULL THEN 1 END) as non_null_count,
                        COUNT(CASE WHEN opening_credit_amount > 0 THEN 1 END) as positive_count,
                        COUNT(CASE WHEN opening_credit_amount < 0 THEN 1 END) as negative_count,
                        COUNT(CASE WHEN opening_credit_amount = 0 THEN 1 END) as zero_count,
                        MIN(opening_credit_amount) as min_value,
                        MAX(opening_credit_amount) as max_value,
                        AVG(opening_credit_amount) as avg_value
                    FROM financial_data 
                    WHERE year = 2024 AND month = 2 
                    AND account_full_name LIKE '%主营业务收入%'
                """)
                stats = cursor.fetchone()
                print(f"  总记录数: {stats[0]}")
                print(f"  非空值数量: {stats[1]}")
                print(f"  正值数量: {stats[2]}")
                print(f"  负值数量: {stats[3]}")
                print(f"  零值数量: {stats[4]}")
                print(f"  最小值: {stats[5]}")
                print(f"  最大值: {stats[6]}")
                print(f"  平均值: {stats[7]}")
                
                # 9. 执行原始查询
                print(f"\n9. 执行原始查询:")
                cursor.execute("""
                    SELECT 
                        accounting_unit_name, 
                        SUM(opening_credit_amount) AS total_opening_credit_amount
                    FROM 
                        financial_data
                    WHERE 
                        year = 2024 
                        AND month = 2 
                        AND account_full_name LIKE '主营业务收入%'
                    GROUP BY 
                        accounting_unit_name
                    ORDER BY 
                        total_opening_credit_amount DESC
                """)
                results = cursor.fetchall()
                print(f"查询结果数量: {len(results)}")
                for i, (unit_name, total_amount) in enumerate(results[:10]):
                    print(f"  {i+1}. {unit_name}: {total_amount}")
                
                # 10. 检查是否有其他金额字段有数据
                print(f"\n10. 检查其他金额字段:")
                cursor.execute("""
                    SELECT 
                        SUM(opening_debit_amount) as total_opening_debit,
                        SUM(debit_amount) as total_debit,
                        SUM(credit_amount) as total_credit,
                        SUM(debit_cumulative) as total_debit_cumulative,
                        SUM(credit_cumulative) as total_credit_cumulative
                    FROM financial_data 
                    WHERE year = 2024 AND month = 2 
                    AND account_full_name LIKE '%主营业务收入%'
                """)
                other_amounts = cursor.fetchone()
                print(f"  期初借方金额总和: {other_amounts[0]}")
                print(f"  借方金额总和: {other_amounts[1]}")
                print(f"  贷方金额总和: {other_amounts[2]}")
                print(f"  借方累计总和: {other_amounts[3]}")
                print(f"  贷方累计总和: {other_amounts[4]}")
                
            else:
                print("❌ 2024年2月没有数据")
        else:
            print("❌ 2024年没有数据")
            
        # 11. 检查所有包含"主营业务收入"的记录
        print(f"\n11. 检查所有包含'主营业务收入'的记录:")
        cursor.execute("""
            SELECT year, month, COUNT(*), SUM(opening_credit_amount)
            FROM financial_data 
            WHERE account_full_name LIKE '%主营业务收入%'
            GROUP BY year, month
            ORDER BY year, month
        """)
        all_revenue_data = cursor.fetchall()
        print("年月分布:")
        for year, month, count, total_amount in all_revenue_data:
            print(f"  {year}年{month}月: {count} 条记录, 期初贷方金额总和: {total_amount}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 分析过程中出错: {str(e)}")

if __name__ == "__main__":
    analyze_sql_issue()
