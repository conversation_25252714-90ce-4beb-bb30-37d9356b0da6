#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os

def analyze_database_tables():
    """分析数据库中所有表的结构和作用"""
    
    db_path = 'fin_data.db'
    if not os.path.exists(db_path):
        print(f'数据库文件不存在: {db_path}')
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("=" * 80)
        print("数据库表结构分析")
        print("=" * 80)
        
        # 获取所有表名
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name")
        tables = cursor.fetchall()
        
        print(f"\n📊 数据库包含 {len(tables)} 个表:")
        for i, table in enumerate(tables, 1):
            print(f"{i:2d}. {table[0]}")
        
        print("\n" + "=" * 80)
        print("详细表结构分析")
        print("=" * 80)
        
        # 分类表格
        business_tables = []
        metadata_tables = []
        system_tables = []
        orm_tables = []

        for table_name in [t[0] for t in tables]:
            if table_name == 'financial_data':
                business_tables.append(table_name)
            elif table_name in ['table_descriptions', 'column_descriptions', 'business_rules']:
                metadata_tables.append(table_name)
            elif table_name in ['sqlite_sequence', 'alembic_version']:
                system_tables.append(table_name)
            else:
                orm_tables.append(table_name)

        # 分析各类表
        table_categories = [
            ("💼 业务数据表", business_tables),
            ("🗃️ 元数据表", metadata_tables),
            ("⚙️ ORM管理表", orm_tables),
            ("🔧 系统表", system_tables)
        ]

        for category_name, table_list in table_categories:
            if table_list:
                print(f"\n{category_name}:")
                for table_name in table_list:
                    analyze_single_table(cursor, table_name)

        conn.close()

    except Exception as e:
        print(f"分析数据库时出错: {e}")

def analyze_single_table(cursor, table_name):
    """分析单个表的详细信息"""
    print(f"\n🔍 表名: {table_name}")
    print("-" * 60)

    # 获取表结构
    cursor.execute(f"PRAGMA table_info({table_name})")
    columns = cursor.fetchall()

    print("📋 字段结构:")
    for col in columns:
        col_id, name, data_type, not_null, default_val, is_pk = col
        pk_flag = " [PK]" if is_pk else ""
        null_flag = " NOT NULL" if not_null else ""
        default_flag = f" DEFAULT {default_val}" if default_val else ""
        print(f"  • {name} ({data_type}){pk_flag}{null_flag}{default_flag}")

    # 获取记录数量
    try:
        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        count = cursor.fetchone()[0]
        print(f"📈 记录数量: {count:,}")
    except Exception as e:
        print(f"📈 记录数量: 无法获取 ({e})")

    # 分析表的作用
    analyze_table_purpose(table_name, len(columns), count if 'count' in locals() else 0)

    # 如果是小表，显示示例数据
    if table_name in ['table_descriptions', 'business_rules', 'column_descriptions'] and count < 50:
        try:
            cursor.execute(f"SELECT * FROM {table_name} LIMIT 3")
            sample_data = cursor.fetchall()
            if sample_data:
                print("📝 示例数据:")
                for i, row in enumerate(sample_data, 1):
                    print(f"  {i}. {str(row)[:200]}{'...' if len(str(row)) > 200 else ''}")
        except Exception as e:
            print(f"📝 示例数据: 无法获取 ({e})")

def analyze_table_purpose(table_name, column_count, record_count):
    """分析表的业务作用"""
    purposes = {
        'financial_data': '💰 核心业务表：存储财务辅助科目余额数据，包含31个字段的完整财务信息',
        'table_descriptions': '📊 表元数据：存储数据库表的描述信息，为AI提供表级别的语义理解',
        'column_descriptions': '📋 字段元数据：存储每个字段的详细描述和AI理解要点，支持智能SQL生成',
        'business_rules': '⚖️ 业务规则：存储财务业务规则，确保AI生成的SQL符合财务逻辑',
        'dbconnection': '🔗 数据库连接：管理多数据库连接配置，支持SQLite/MySQL/PostgreSQL',
        'schematable': '🗂️ 表结构管理：存储数据库表的结构信息，支持动态Schema发现',
        'schemacolumn': '📝 字段管理：存储表字段的详细信息，包括数据类型和约束',
        'schemarelationship': '🔗 关系管理：存储表间关系，支持复杂查询的关联分析',
        'valuemapping': '🔄 值映射：存储自然语言术语与数据库值的映射关系',
        'chatsession': '💬 会话管理：存储用户聊天会话信息，支持多轮对话',
        'chatmessage': '📨 消息记录：存储聊天消息详情，包括查询、SQL、结果等',
        'chathistorysnapshot': '📸 历史快照：存储查询历史的快照数据，支持会话恢复',
        'sqlite_sequence': '🔢 SQLite系统表：管理自增ID序列',
        'alembic_version': '🔄 版本控制：Alembic数据库迁移版本管理'
    }

    purpose = purposes.get(table_name, '❓ 未知用途的表')
    print(f"🎯 表作用: {purpose}")
        
        conn.close()
        
    except Exception as e:
        print(f"分析数据库时出错: {e}")

if __name__ == "__main__":
    analyze_database_tables()
