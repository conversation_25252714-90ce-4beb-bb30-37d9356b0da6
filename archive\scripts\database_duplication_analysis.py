#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
数据库重复问题详细排查脚本
分析 fin_data.db 和 resource.db 的状态，检查数据重复问题
"""

import sqlite3
import os
import hashlib
from datetime import datetime
from typing import Dict, List, Tuple, Any

class DatabaseDuplicationAnalyzer:
    """数据库重复问题分析器"""
    
    def __init__(self):
        self.fin_data_db = "fin_data.db"
        self.resource_db = "resource.db"
        self.analysis_results = {}
        
    def check_database_existence(self):
        """检查数据库文件是否存在"""
        print("🔍 检查数据库文件存在性")
        print("=" * 60)
        
        fin_exists = os.path.exists(self.fin_data_db)
        resource_exists = os.path.exists(self.resource_db)
        
        print(f"fin_data.db: {'✅ 存在' if fin_exists else '❌ 不存在'}")
        if fin_exists:
            size = os.path.getsize(self.fin_data_db)
            print(f"  文件大小: {size:,} 字节 ({size/1024/1024:.2f} MB)")
        
        print(f"resource.db: {'✅ 存在' if resource_exists else '❌ 不存在'}")
        if resource_exists:
            size = os.path.getsize(self.resource_db)
            print(f"  文件大小: {size:,} 字节 ({size/1024/1024:.2f} MB)")
        
        return fin_exists, resource_exists
    
    def get_database_schema(self, db_path: str) -> Dict[str, Any]:
        """获取数据库的完整schema信息"""
        if not os.path.exists(db_path):
            return {"tables": {}, "views": {}, "indexes": {}}
        
        try:
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            schema_info = {"tables": {}, "views": {}, "indexes": {}}
            
            # 获取所有表
            cursor.execute("""
                SELECT name, sql FROM sqlite_master 
                WHERE type='table' AND name NOT LIKE 'sqlite_%'
                ORDER BY name
            """)
            
            for table_name, create_sql in cursor.fetchall():
                # 获取表的记录数
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                record_count = cursor.fetchone()[0]
                
                # 获取表结构
                cursor.execute(f"PRAGMA table_info({table_name})")
                columns = cursor.fetchall()
                
                schema_info["tables"][table_name] = {
                    "record_count": record_count,
                    "create_sql": create_sql,
                    "columns": [
                        {
                            "name": col[1],
                            "type": col[2],
                            "not_null": col[3],
                            "default": col[4],
                            "pk": col[5]
                        }
                        for col in columns
                    ]
                }
            
            # 获取所有视图
            cursor.execute("""
                SELECT name, sql FROM sqlite_master 
                WHERE type='view'
                ORDER BY name
            """)
            
            for view_name, create_sql in cursor.fetchall():
                schema_info["views"][view_name] = {
                    "create_sql": create_sql
                }
            
            # 获取所有索引
            cursor.execute("""
                SELECT name, sql FROM sqlite_master 
                WHERE type='index' AND name NOT LIKE 'sqlite_%'
                ORDER BY name
            """)
            
            for index_name, create_sql in cursor.fetchall():
                if create_sql:  # 排除自动创建的索引
                    schema_info["indexes"][index_name] = {
                        "create_sql": create_sql
                    }
            
            conn.close()
            return schema_info
            
        except Exception as e:
            print(f"❌ 获取 {db_path} schema失败: {e}")
            return {"tables": {}, "views": {}, "indexes": {}}
    
    def analyze_database_schemas(self):
        """分析两个数据库的schema"""
        print("\n📊 分析数据库Schema")
        print("=" * 60)
        
        fin_schema = self.get_database_schema(self.fin_data_db)
        resource_schema = self.get_database_schema(self.resource_db)
        
        # 分析fin_data.db
        print(f"\n🗃️ fin_data.db 分析:")
        print(f"  表数量: {len(fin_schema['tables'])}")
        print(f"  视图数量: {len(fin_schema['views'])}")
        print(f"  索引数量: {len(fin_schema['indexes'])}")
        
        print(f"\n  📋 表详情:")
        for table_name, info in fin_schema["tables"].items():
            print(f"    {table_name}: {info['record_count']:,} 条记录")
        
        # 分析resource.db
        print(f"\n🗃️ resource.db 分析:")
        print(f"  表数量: {len(resource_schema['tables'])}")
        print(f"  视图数量: {len(resource_schema['views'])}")
        print(f"  索引数量: {len(resource_schema['indexes'])}")
        
        print(f"\n  📋 表详情:")
        for table_name, info in resource_schema["tables"].items():
            print(f"    {table_name}: {info['record_count']:,} 条记录")
        
        self.analysis_results["fin_schema"] = fin_schema
        self.analysis_results["resource_schema"] = resource_schema
        
        return fin_schema, resource_schema
    
    def find_duplicate_tables(self, fin_schema: Dict, resource_schema: Dict):
        """查找重复的表"""
        print("\n🔍 查找重复表")
        print("=" * 60)
        
        fin_tables = set(fin_schema["tables"].keys())
        resource_tables = set(resource_schema["tables"].keys())
        
        # 找到重复的表
        duplicate_tables = fin_tables.intersection(resource_tables)
        
        # 只在fin_data.db中的表
        fin_only = fin_tables - resource_tables
        
        # 只在resource.db中的表
        resource_only = resource_tables - fin_tables
        
        print(f"📊 重复表数量: {len(duplicate_tables)}")
        if duplicate_tables:
            print(f"  重复表列表: {list(duplicate_tables)}")
        
        print(f"\n📊 仅在fin_data.db中的表 ({len(fin_only)}个):")
        for table in sorted(fin_only):
            count = fin_schema["tables"][table]["record_count"]
            print(f"  {table}: {count:,} 条记录")
        
        print(f"\n📊 仅在resource.db中的表 ({len(resource_only)}个):")
        for table in sorted(resource_only):
            count = resource_schema["tables"][table]["record_count"]
            print(f"  {table}: {count:,} 条记录")
        
        self.analysis_results["duplicate_tables"] = duplicate_tables
        self.analysis_results["fin_only_tables"] = fin_only
        self.analysis_results["resource_only_tables"] = resource_only
        
        return duplicate_tables, fin_only, resource_only
    
    def compare_duplicate_table_data(self, duplicate_tables: set):
        """比较重复表中的数据内容"""
        if not duplicate_tables:
            print("\n✅ 没有发现重复表")
            return
        
        print(f"\n🔍 比较重复表数据内容")
        print("=" * 60)
        
        for table_name in duplicate_tables:
            print(f"\n📋 分析表: {table_name}")
            
            try:
                # 连接两个数据库
                fin_conn = sqlite3.connect(self.fin_data_db)
                resource_conn = sqlite3.connect(self.resource_db)
                
                fin_cursor = fin_conn.cursor()
                resource_cursor = resource_conn.cursor()
                
                # 获取记录数
                fin_cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                fin_count = fin_cursor.fetchone()[0]
                
                resource_cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                resource_count = resource_cursor.fetchone()[0]
                
                print(f"  fin_data.db: {fin_count:,} 条记录")
                print(f"  resource.db: {resource_count:,} 条记录")
                
                if fin_count != resource_count:
                    print(f"  ⚠️  记录数不一致！")
                else:
                    print(f"  ✅ 记录数一致")
                
                # 比较数据内容（通过hash）
                if fin_count > 0 and resource_count > 0:
                    # 获取所有数据并计算hash
                    fin_cursor.execute(f"SELECT * FROM {table_name} ORDER BY rowid")
                    fin_data = fin_cursor.fetchall()
                    
                    resource_cursor.execute(f"SELECT * FROM {table_name} ORDER BY rowid")
                    resource_data = resource_cursor.fetchall()
                    
                    fin_hash = hashlib.md5(str(fin_data).encode()).hexdigest()
                    resource_hash = hashlib.md5(str(resource_data).encode()).hexdigest()
                    
                    if fin_hash == resource_hash:
                        print(f"  ✅ 数据内容完全一致")
                    else:
                        print(f"  ⚠️  数据内容不一致！")
                        print(f"    fin_data.db hash: {fin_hash[:16]}...")
                        print(f"    resource.db hash: {resource_hash[:16]}...")
                        
                        # 如果数据量不大，显示具体差异
                        if fin_count <= 100 and resource_count <= 100:
                            self._show_data_differences(table_name, fin_data, resource_data)
                
                fin_conn.close()
                resource_conn.close()
                
            except Exception as e:
                print(f"  ❌ 比较失败: {e}")
    
    def _show_data_differences(self, table_name: str, fin_data: List, resource_data: List):
        """显示数据差异的详细信息"""
        print(f"    🔍 详细差异分析:")
        
        fin_set = set(fin_data)
        resource_set = set(resource_data)
        
        only_in_fin = fin_set - resource_set
        only_in_resource = resource_set - fin_set
        
        if only_in_fin:
            print(f"    仅在fin_data.db中: {len(only_in_fin)} 条记录")
            for i, record in enumerate(list(only_in_fin)[:3]):  # 只显示前3条
                print(f"      {i+1}. {record}")
            if len(only_in_fin) > 3:
                print(f"      ... 还有 {len(only_in_fin)-3} 条")
        
        if only_in_resource:
            print(f"    仅在resource.db中: {len(only_in_resource)} 条记录")
            for i, record in enumerate(list(only_in_resource)[:3]):  # 只显示前3条
                print(f"      {i+1}. {record}")
            if len(only_in_resource) > 3:
                print(f"      ... 还有 {len(only_in_resource)-3} 条")
    
    def check_migration_history(self):
        """检查迁移历史和备份文件"""
        print(f"\n📜 检查迁移历史")
        print("=" * 60)
        
        # 查找备份文件
        backup_files = []
        for file in os.listdir("."):
            if file.endswith(".backup_20250627_085551") or "backup" in file:
                backup_files.append(file)
        
        print(f"📁 发现备份文件 ({len(backup_files)}个):")
        for backup in sorted(backup_files):
            if os.path.exists(backup):
                size = os.path.getsize(backup)
                mtime = datetime.fromtimestamp(os.path.getmtime(backup))
                print(f"  {backup}: {size:,} 字节, 修改时间: {mtime}")
            else:
                print(f"  {backup}: ❌ 文件不存在")
        
        # 检查配置文件备份
        env_backup = "chatdb/backend/.env.backup_20250627_085551"
        if os.path.exists(env_backup):
            print(f"\n⚙️ 配置文件备份存在: {env_backup}")
            with open(env_backup, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    if 'SQLITE_DB_PATH' in line:
                        print(f"  第{line_num}行: {line.strip()}")
        
        self.analysis_results["backup_files"] = backup_files
    
    def analyze_architecture_compliance(self):
        """分析当前状态是否符合预期架构"""
        print(f"\n🏗️ 架构合规性分析")
        print("=" * 60)
        
        fin_schema = self.analysis_results.get("fin_schema", {})
        resource_schema = self.analysis_results.get("resource_schema", {})
        
        # 预期的架构设计
        expected_business_tables = {"financial_data"}
        expected_metadata_tables = {"table_descriptions", "column_descriptions", "business_rules"}
        expected_system_tables = {"dbconnection", "schematable", "schemacolumn", "schemarelationship", 
                                "valuemapping", "chatsession", "chatmessage"}
        
        print(f"📊 预期架构:")
        print(f"  fin_data.db应包含: 业务数据表 {expected_business_tables}")
        print(f"  resource.db应包含: 元数据表 {expected_metadata_tables} + 系统表 {expected_system_tables}")
        
        # 检查实际状态
        fin_tables = set(fin_schema.get("tables", {}).keys())
        resource_tables = set(resource_schema.get("tables", {}).keys())
        
        print(f"\n📊 实际状态:")
        print(f"  fin_data.db包含: {fin_tables}")
        print(f"  resource.db包含: {resource_tables}")
        
        # 合规性检查
        compliance_issues = []
        
        # 检查业务数据是否在正确位置
        business_in_fin = expected_business_tables.intersection(fin_tables)
        business_in_resource = expected_business_tables.intersection(resource_tables)
        
        if business_in_resource:
            compliance_issues.append(f"❌ 业务表在resource.db中: {business_in_resource}")
        if business_in_fin:
            print(f"✅ 业务表正确位于fin_data.db: {business_in_fin}")
        
        # 检查元数据是否在正确位置
        metadata_in_resource = expected_metadata_tables.intersection(resource_tables)
        metadata_in_fin = expected_metadata_tables.intersection(fin_tables)
        
        if metadata_in_fin:
            compliance_issues.append(f"❌ 元数据表在fin_data.db中: {metadata_in_fin}")
        if metadata_in_resource:
            print(f"✅ 元数据表正确位于resource.db: {metadata_in_resource}")
        
        # 检查系统表
        system_in_resource = expected_system_tables.intersection(resource_tables)
        if system_in_resource:
            print(f"✅ 系统表正确位于resource.db: {len(system_in_resource)}个")
        
        if compliance_issues:
            print(f"\n⚠️  发现架构合规性问题:")
            for issue in compliance_issues:
                print(f"  {issue}")
        else:
            print(f"\n✅ 架构完全符合预期设计")
        
        self.analysis_results["compliance_issues"] = compliance_issues
        return compliance_issues
    
    def generate_cleanup_recommendations(self):
        """生成清理建议"""
        print(f"\n🛠️ 清理建议")
        print("=" * 60)
        
        duplicate_tables = self.analysis_results.get("duplicate_tables", set())
        compliance_issues = self.analysis_results.get("compliance_issues", [])
        
        recommendations = []
        
        if duplicate_tables:
            print(f"🔧 重复表清理建议:")
            for table in duplicate_tables:
                fin_schema = self.analysis_results["fin_schema"]
                resource_schema = self.analysis_results["resource_schema"]
                
                fin_count = fin_schema["tables"][table]["record_count"]
                resource_count = resource_schema["tables"][table]["record_count"]
                
                if table in {"table_descriptions", "column_descriptions", "business_rules"}:
                    # 元数据表应该在resource.db中
                    if fin_count > 0:
                        recommendations.append(f"删除fin_data.db中的{table}表")
                        print(f"  建议删除fin_data.db中的{table}表 ({fin_count}条记录)")
                elif table == "financial_data":
                    # 业务数据应该在fin_data.db中
                    if resource_count > 0:
                        recommendations.append(f"删除resource.db中的{table}表")
                        print(f"  建议删除resource.db中的{table}表 ({resource_count}条记录)")
                else:
                    # 其他表需要具体分析
                    print(f"  {table}表需要手动决定保留位置")
        
        if not duplicate_tables and not compliance_issues:
            print(f"✅ 当前状态良好，无需清理")
        
        self.analysis_results["recommendations"] = recommendations
        return recommendations
    
    def run_full_analysis(self):
        """运行完整分析"""
        print("🎯 数据库重复问题详细排查")
        print("=" * 80)
        
        # 1. 检查数据库存在性
        fin_exists, resource_exists = self.check_database_existence()
        
        if not (fin_exists and resource_exists):
            print("❌ 数据库文件缺失，无法进行完整分析")
            return
        
        # 2. 分析数据库schema
        fin_schema, resource_schema = self.analyze_database_schemas()
        
        # 3. 查找重复表
        duplicate_tables, fin_only, resource_only = self.find_duplicate_tables(fin_schema, resource_schema)
        
        # 4. 比较重复表数据
        self.compare_duplicate_table_data(duplicate_tables)
        
        # 5. 检查迁移历史
        self.check_migration_history()
        
        # 6. 架构合规性分析
        compliance_issues = self.analyze_architecture_compliance()
        
        # 7. 生成清理建议
        recommendations = self.generate_cleanup_recommendations()
        
        # 8. 生成总结报告
        self.generate_summary_report()
    
    def generate_summary_report(self):
        """生成总结报告"""
        print(f"\n📊 总结报告")
        print("=" * 80)
        
        duplicate_tables = self.analysis_results.get("duplicate_tables", set())
        compliance_issues = self.analysis_results.get("compliance_issues", [])
        recommendations = self.analysis_results.get("recommendations", [])
        
        print(f"🔍 分析结果:")
        print(f"  重复表数量: {len(duplicate_tables)}")
        print(f"  架构合规问题: {len(compliance_issues)}")
        print(f"  清理建议数量: {len(recommendations)}")
        
        if duplicate_tables or compliance_issues:
            print(f"\n⚠️  需要关注的问题:")
            if duplicate_tables:
                print(f"  - 发现 {len(duplicate_tables)} 个重复表")
            if compliance_issues:
                print(f"  - 发现 {len(compliance_issues)} 个架构合规问题")
        else:
            print(f"\n✅ 数据库状态良好，符合预期架构")
        
        print(f"\n📋 下一步行动:")
        if recommendations:
            for i, rec in enumerate(recommendations, 1):
                print(f"  {i}. {rec}")
        else:
            print(f"  无需特殊行动，当前状态符合预期")

if __name__ == "__main__":
    analyzer = DatabaseDuplicationAnalyzer()
    analyzer.run_full_analysis()
