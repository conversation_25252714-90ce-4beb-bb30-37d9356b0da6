#!/usr/bin/env python3
"""
修改 main.financial_data 表中 balance 字段类型从 TEXT 改为 REAL

作者: AI Assistant
日期: 2025-06-25
"""

import sqlite3
import shutil
from datetime import datetime
import os


def backup_database(db_path):
    """备份数据库"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_path = f"{db_path}.backup_{timestamp}"
    shutil.copy2(db_path, backup_path)
    print(f"数据库已备份到: {backup_path}")
    return backup_path


def get_table_structure(cursor, table_name):
    """获取表结构"""
    cursor.execute(f"PRAGMA table_info({table_name})")
    return cursor.fetchall()


def create_new_table_sql(table_name, columns):
    """生成创建新表的SQL语句，将balance字段类型改为REAL"""
    column_definitions = []
    
    for col in columns:
        cid, name, data_type, notnull, default_value, pk = col
        
        # 修改balance字段类型为REAL
        if name == 'balance':
            data_type = 'REAL'
        
        # 构建列定义
        col_def = f"{name} {data_type}"
        
        if notnull:
            col_def += " NOT NULL"
        
        if default_value is not None:
            col_def += f" DEFAULT {default_value}"
        
        if pk:
            col_def += " PRIMARY KEY"
        
        column_definitions.append(col_def)
    
    return f"CREATE TABLE {table_name}_new ({', '.join(column_definitions)})"


def modify_balance_field_type(db_path='fin_data.db'):
    """修改balance字段类型"""
    
    # 检查数据库文件是否存在
    if not os.path.exists(db_path):
        print(f"错误: 数据库文件 {db_path} 不存在")
        return False
    
    # 备份数据库
    backup_path = backup_database(db_path)
    
    try:
        # 连接数据库
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 开始事务
        cursor.execute("BEGIN TRANSACTION")
        
        # 获取原表结构
        print("获取原表结构...")
        original_columns = get_table_structure(cursor, 'financial_data')
        print(f"原表有 {len(original_columns)} 列")
        
        # 检查balance字段当前类型
        balance_col = next((col for col in original_columns if col[1] == 'balance'), None)
        if balance_col:
            print(f"Balance字段当前类型: {balance_col[2]}")
        else:
            print("错误: 未找到balance字段")
            return False
        
        # 创建新表
        print("创建新表结构...")
        create_sql = create_new_table_sql('financial_data', original_columns)
        print(f"新表创建语句: {create_sql}")
        cursor.execute(create_sql)
        
        # 获取列名列表
        column_names = [col[1] for col in original_columns]
        columns_str = ', '.join(column_names)
        
        # 迁移数据，将balance字段从TEXT转换为REAL
        print("迁移数据...")
        migrate_sql = f"""
        INSERT INTO financial_data_new ({columns_str})
        SELECT {', '.join([
            f"CAST({name} AS REAL)" if name == 'balance' else name 
            for name in column_names
        ])}
        FROM financial_data
        """
        
        cursor.execute(migrate_sql)
        migrated_rows = cursor.rowcount
        print(f"已迁移 {migrated_rows} 行数据")
        
        # 验证数据完整性
        print("验证数据完整性...")
        cursor.execute("SELECT COUNT(*) FROM financial_data")
        original_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM financial_data_new")
        new_count = cursor.fetchone()[0]
        
        if original_count != new_count:
            raise Exception(f"数据迁移失败: 原表 {original_count} 行，新表 {new_count} 行")
        
        print(f"数据完整性验证通过: {new_count} 行")
        
        # 删除原表
        print("删除原表...")
        cursor.execute("DROP TABLE financial_data")
        
        # 重命名新表
        print("重命名新表...")
        cursor.execute("ALTER TABLE financial_data_new RENAME TO financial_data")
        
        # 提交事务
        cursor.execute("COMMIT")
        
        # 验证新表结构
        print("验证新表结构...")
        new_columns = get_table_structure(cursor, 'financial_data')
        balance_new_col = next((col for col in new_columns if col[1] == 'balance'), None)
        if balance_new_col:
            print(f"Balance字段新类型: {balance_new_col[2]}")
        
        # 测试数据查询
        print("测试数据查询...")
        cursor.execute("SELECT balance FROM financial_data WHERE balance != 0 LIMIT 5")
        sample_data = cursor.fetchall()
        print("示例balance数据:")
        for row in sample_data:
            print(f"  {row[0]} (类型: {type(row[0])})")
        
        conn.close()
        
        print("\n=== 修改完成 ===")
        print(f"✅ Balance字段类型已从 TEXT 修改为 REAL")
        print(f"✅ 数据完整性验证通过")
        print(f"✅ 备份文件: {backup_path}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 修改失败: {str(e)}")
        
        # 回滚操作
        try:
            conn.rollback()
            conn.close()
        except:
            pass
        
        # 恢复备份
        print("正在恢复备份...")
        shutil.copy2(backup_path, db_path)
        print("备份已恢复")
        
        return False


if __name__ == "__main__":
    print("开始修改 balance 字段类型...")
    success = modify_balance_field_type()
    
    if success:
        print("\n🎉 修改成功完成!")
    else:
        print("\n💥 修改失败，已恢复备份")
