#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import shutil
import os
from datetime import datetime

# 中文列名到英文列名的映射
COLUMN_MAPPING = {
    '﻿年': 'year',
    '月': 'month',
    '核算组织': 'accounting_organization',
    '核算单位名称': 'accounting_unit_name',
    '科目编号': 'account_code',
    '科目全称': 'account_full_name',
    '科目名称': 'account_name',
    '期初借方金额': 'opening_debit_amount',
    '期初贷方金额': 'opening_credit_amount',
    '科目方向': 'account_direction',
    '项目ID': 'project_id',
    '项目编号': 'project_code',
    '项目名称': 'project_name',
    '市场性质ID': 'market_nature_id',
    '税率ID': 'tax_rate_id',
    '税率名称': 'tax_rate_name',
    '业态ID': 'business_format_id',
    '金融产品ID': 'financial_product_id',
    '长期待摊项目ID': 'long_term_deferred_project_id',
    '楼盘房号ID': 'property_unit_id',
    '现金流量项目ID': 'cash_flow_project_id',
    '市属国企单位ID': 'municipal_enterprise_unit_id',
    '银行账号ID': 'bank_account_id',
    '金融机构ID': 'financial_institution_id',
    '联行号': 'bank_routing_number',
    '银行名称': 'bank_name',
    '借方金额': 'debit_amount',
    '借方累计': 'debit_cumulative',
    '贷方金额': 'credit_amount',
    '贷方累计': 'credit_cumulative',
    '余额': 'balance'
}

def backup_database(db_path):
    """备份数据库"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_path = f"{db_path}.backup_{timestamp}"
    shutil.copy2(db_path, backup_path)
    print(f"数据库已备份到: {backup_path}")
    return backup_path

def get_current_columns(cursor, table_name):
    """获取当前表的列信息"""
    cursor.execute(f"PRAGMA table_info({table_name})")
    columns = cursor.fetchall()
    return [(col[1], col[2]) for col in columns]  # (name, type)

def rename_table_columns(db_path, table_name='financial_data'):
    """重命名表的所有列"""
    
    # 备份数据库
    backup_path = backup_database(db_path)
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取当前列信息
        current_columns = get_current_columns(cursor, table_name)
        print(f"当前表 {table_name} 有 {len(current_columns)} 列")
        
        # 开始事务
        cursor.execute("BEGIN TRANSACTION")
        
        # 创建新表结构
        new_columns = []
        for col_name, col_type in current_columns:
            new_name = COLUMN_MAPPING.get(col_name, col_name)
            new_columns.append(f'"{new_name}" {col_type}')
            print(f"  {col_name} -> {new_name}")
        
        # 创建新表
        new_table_name = f"{table_name}_new"
        create_sql = f"CREATE TABLE {new_table_name} ({', '.join(new_columns)})"
        print(f"\n创建新表SQL: {create_sql}")
        cursor.execute(create_sql)
        
        # 准备数据迁移的列名映射
        old_column_names = [f'"{col[0]}"' for col in current_columns]
        new_column_names = [f'"{COLUMN_MAPPING.get(col[0], col[0])}"' for col in current_columns]
        
        # 迁移数据
        insert_sql = f"""
        INSERT INTO {new_table_name} ({', '.join(new_column_names)})
        SELECT {', '.join(old_column_names)}
        FROM {table_name}
        """
        print(f"\n数据迁移SQL: {insert_sql}")
        cursor.execute(insert_sql)
        
        # 获取迁移的行数
        cursor.execute(f"SELECT COUNT(*) FROM {new_table_name}")
        row_count = cursor.fetchone()[0]
        print(f"成功迁移 {row_count} 行数据")
        
        # 删除旧表
        cursor.execute(f"DROP TABLE {table_name}")
        
        # 重命名新表
        cursor.execute(f"ALTER TABLE {new_table_name} RENAME TO {table_name}")
        
        # 提交事务
        cursor.execute("COMMIT")
        
        print(f"\n✅ 成功重命名 {table_name} 表的所有列!")
        
        # 验证结果
        verify_rename_result(cursor, table_name)
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 重命名过程中出错: {str(e)}")
        print(f"可以从备份文件恢复: {backup_path}")
        raise

def verify_rename_result(cursor, table_name):
    """验证重命名结果"""
    print(f"\n=== 验证 {table_name} 表重命名结果 ===")
    
    # 获取新的列信息
    cursor.execute(f"PRAGMA table_info({table_name})")
    columns = cursor.fetchall()
    
    print("新的列结构:")
    for col in columns:
        print(f"  {col[1]} ({col[2]})")
    
    # 检查数据完整性
    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
    total_rows = cursor.fetchone()[0]
    print(f"\n数据行数: {total_rows}")
    
    # 显示前3行数据
    cursor.execute(f"SELECT * FROM {table_name} LIMIT 3")
    sample_data = cursor.fetchall()
    print(f"\n示例数据 (前3行):")
    for i, row in enumerate(sample_data, 1):
        print(f"  行 {i}: {row[:5]}...")  # 只显示前5列避免输出过长

def print_column_mapping():
    """打印列名映射表"""
    print("\n=== 列名映射表 ===")
    print("中文列名 -> 英文列名")
    print("-" * 50)
    for chinese, english in COLUMN_MAPPING.items():
        print(f"{chinese:15} -> {english}")

if __name__ == "__main__":
    db_path = "fin_data.db"
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        exit(1)
    
    print("🚀 开始重命名 financial_data 表的列名...")
    print_column_mapping()
    
    # 确认操作
    response = input(f"\n是否继续重命名 {db_path} 中的 financial_data 表列名? (y/N): ")
    if response.lower() != 'y':
        print("操作已取消")
        exit(0)
    
    try:
        rename_table_columns(db_path)
        print("\n🎉 列名重命名完成!")
    except Exception as e:
        print(f"\n❌ 操作失败: {str(e)}")
