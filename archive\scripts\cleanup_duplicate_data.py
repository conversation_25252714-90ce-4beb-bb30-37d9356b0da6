#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
数据库重复数据清理脚本
基于分析结果清理重复数据，确保架构合规
"""

import sqlite3
import os
import shutil
from datetime import datetime

class DatabaseCleanup:
    """数据库清理工具"""
    
    def __init__(self):
        self.fin_data_db = "fin_data.db"
        self.resource_db = "resource.db"
        
    def backup_databases(self):
        """在清理前备份数据库"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        print("🔒 清理前备份数据库...")
        
        # 备份fin_data.db
        if os.path.exists(self.fin_data_db):
            backup_path = f"{self.fin_data_db}.cleanup_backup_{timestamp}"
            shutil.copy2(self.fin_data_db, backup_path)
            print(f"✅ fin_data.db备份到: {backup_path}")
        
        # 备份resource.db
        if os.path.exists(self.resource_db):
            backup_path = f"{self.resource_db}.cleanup_backup_{timestamp}"
            shutil.copy2(self.resource_db, backup_path)
            print(f"✅ resource.db备份到: {backup_path}")
    
    def clean_metadata_tables_from_fin_data(self):
        """从fin_data.db中清理元数据表"""
        print("\n🧹 清理fin_data.db中的元数据表...")
        
        metadata_tables_to_remove = [
            'table_descriptions',
            'column_descriptions'
        ]
        
        try:
            conn = sqlite3.connect(self.fin_data_db)
            cursor = conn.cursor()
            
            for table_name in metadata_tables_to_remove:
                # 检查表是否存在
                cursor.execute("""
                    SELECT name FROM sqlite_master 
                    WHERE type='table' AND name=?
                """, (table_name,))
                
                if cursor.fetchone():
                    # 获取记录数
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                    count = cursor.fetchone()[0]
                    
                    # 删除表
                    cursor.execute(f"DROP TABLE {table_name}")
                    print(f"✅ 删除了fin_data.db中的{table_name}表 ({count}条记录)")
                else:
                    print(f"⚠️  {table_name}表在fin_data.db中不存在")
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"❌ 清理fin_data.db失败: {e}")
    
    def clean_empty_system_tables_from_fin_data(self):
        """从fin_data.db中清理空的系统表"""
        print("\n🧹 清理fin_data.db中的空系统表...")
        
        system_tables_to_check = [
            'dbconnection',
            'schematable', 
            'schemacolumn',
            'schemarelationship',
            'valuemapping',
            'chatmessage',
            'chatsession',
            'chathistorysnapshot'
        ]
        
        try:
            conn = sqlite3.connect(self.fin_data_db)
            cursor = conn.cursor()
            
            for table_name in system_tables_to_check:
                # 检查表是否存在
                cursor.execute("""
                    SELECT name FROM sqlite_master 
                    WHERE type='table' AND name=?
                """, (table_name,))
                
                if cursor.fetchone():
                    # 检查记录数
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                    count = cursor.fetchone()[0]
                    
                    if count == 0:
                        # 删除空表
                        cursor.execute(f"DROP TABLE {table_name}")
                        print(f"✅ 删除了fin_data.db中的空表{table_name}")
                    else:
                        print(f"⚠️  {table_name}表在fin_data.db中有{count}条记录，保留")
                else:
                    print(f"⚠️  {table_name}表在fin_data.db中不存在")
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"❌ 清理空系统表失败: {e}")
    
    def clean_views_from_fin_data(self):
        """从fin_data.db中清理视图"""
        print("\n🧹 清理fin_data.db中的视图...")
        
        try:
            conn = sqlite3.connect(self.fin_data_db)
            cursor = conn.cursor()
            
            # 获取所有视图
            cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='view'
            """)
            
            views = cursor.fetchall()
            
            for view_name, in views:
                cursor.execute(f"DROP VIEW {view_name}")
                print(f"✅ 删除了fin_data.db中的视图{view_name}")
            
            if not views:
                print("⚠️  fin_data.db中没有视图需要清理")
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            print(f"❌ 清理视图失败: {e}")
    
    def verify_cleanup_results(self):
        """验证清理结果"""
        print("\n🧪 验证清理结果...")
        
        try:
            # 检查fin_data.db
            fin_conn = sqlite3.connect(self.fin_data_db)
            fin_cursor = fin_conn.cursor()
            
            fin_cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' 
                ORDER BY name
            """)
            fin_tables = [row[0] for row in fin_cursor.fetchall()]
            
            print(f"📊 fin_data.db清理后的表:")
            for table in fin_tables:
                fin_cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = fin_cursor.fetchone()[0]
                print(f"  {table}: {count:,} 条记录")
            
            fin_conn.close()
            
            # 检查resource.db
            resource_conn = sqlite3.connect(self.resource_db)
            resource_cursor = resource_conn.cursor()
            
            resource_cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' 
                ORDER BY name
            """)
            resource_tables = [row[0] for row in resource_cursor.fetchall()]
            
            print(f"\n📊 resource.db的表:")
            for table in resource_tables:
                resource_cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = resource_cursor.fetchone()[0]
                print(f"  {table}: {count:,} 条记录")
            
            resource_conn.close()
            
            # 检查架构合规性
            print(f"\n🏗️ 架构合规性检查:")
            
            # 业务数据表
            if 'financial_data' in fin_tables:
                print(f"✅ 业务数据表正确位于fin_data.db")
            else:
                print(f"❌ 业务数据表不在fin_data.db中")
            
            # 元数据表
            metadata_tables = {'table_descriptions', 'column_descriptions', 'business_rules'}
            metadata_in_resource = metadata_tables.intersection(set(resource_tables))
            metadata_in_fin = metadata_tables.intersection(set(fin_tables))
            
            if metadata_in_resource == metadata_tables:
                print(f"✅ 所有元数据表正确位于resource.db")
            else:
                print(f"⚠️  元数据表分布: resource.db有{metadata_in_resource}, fin_data.db有{metadata_in_fin}")
            
            # 重复表检查
            duplicate_tables = set(fin_tables).intersection(set(resource_tables))
            if duplicate_tables:
                print(f"⚠️  仍有重复表: {duplicate_tables}")
            else:
                print(f"✅ 没有重复表")
            
        except Exception as e:
            print(f"❌ 验证失败: {e}")
    
    def update_configuration(self):
        """更新配置文件确保指向正确的数据库"""
        print(f"\n⚙️ 检查配置文件...")
        
        env_file = "chatdb/backend/.env"
        
        if os.path.exists(env_file):
            with open(env_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if 'SQLITE_DB_PATH=C:\\Users\\<USER>\\PycharmProjects\\智能数据分析系统\\resource.db' in content:
                print(f"✅ 配置文件正确指向resource.db")
            else:
                print(f"⚠️  配置文件可能需要检查")
                # 显示当前配置
                for line_num, line in enumerate(content.split('\n'), 1):
                    if 'SQLITE_DB_PATH' in line:
                        print(f"  第{line_num}行: {line}")
        else:
            print(f"❌ 配置文件不存在: {env_file}")
    
    def run_cleanup(self):
        """运行完整的清理流程"""
        print("🎯 数据库重复数据清理")
        print("=" * 60)
        
        # 1. 备份数据库
        self.backup_databases()
        
        # 2. 清理fin_data.db中的元数据表
        self.clean_metadata_tables_from_fin_data()
        
        # 3. 清理fin_data.db中的空系统表
        self.clean_empty_system_tables_from_fin_data()
        
        # 4. 清理fin_data.db中的视图
        self.clean_views_from_fin_data()
        
        # 5. 验证清理结果
        self.verify_cleanup_results()
        
        # 6. 检查配置文件
        self.update_configuration()
        
        print(f"\n🎉 清理完成！")
        print(f"📋 清理总结:")
        print(f"  - 从fin_data.db中移除了元数据表")
        print(f"  - 清理了空的系统表")
        print(f"  - 确保了架构合规性")
        print(f"  - 保留了完整的备份")

def main():
    """主函数"""
    print("⚠️  即将开始数据库清理操作")
    print("此操作将:")
    print("1. 从fin_data.db中删除元数据表")
    print("2. 清理空的系统表")
    print("3. 确保数据分层架构合规")
    print()
    
    confirm = input("是否继续？(y/N): ").strip().lower()
    if confirm != 'y':
        print("❌ 清理操作已取消")
        return
    
    cleanup = DatabaseCleanup()
    cleanup.run_cleanup()

if __name__ == "__main__":
    main()
