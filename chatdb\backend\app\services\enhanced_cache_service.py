"""
增强缓存服务
支持多层缓存、预加载、智能失效等功能
"""
import asyncio
import logging
import time
import json
import hashlib
from typing import Dict, Any, Optional, List, Callable
from functools import wraps
from concurrent.futures import ThreadPoolExecutor

from app.services.cache_service import cache_service
from app.services.neo4j_connection_pool import get_neo4j_pool

logger = logging.getLogger(__name__)


class EnhancedCacheService:
    """增强缓存服务"""
    
    def __init__(self):
        self.base_cache = cache_service
        self.preload_cache = {}  # 预加载缓存
        self.query_patterns = {}  # 查询模式缓存
        self.executor = ThreadPoolExecutor(max_workers=4)
        
        # 缓存配置
        self.schema_cache_ttl = 14400  # 表结构缓存4小时
        self.qa_cache_ttl = 7200      # 问答对缓存2小时
        self.pattern_cache_ttl = 3600  # 模式缓存1小时
    
    async def get_schema_context_cached(self, connection_id: int, query: str) -> Dict[str, Any]:
        """获取缓存的表结构上下文"""
        cache_key = f"schema_context:{connection_id}:{hashlib.md5(query.encode()).hexdigest()}"
        
        # 尝试从缓存获取
        cached_result = self.base_cache.get(cache_key)
        if cached_result:
            logger.debug(f"表结构缓存命中: {connection_id}")
            return cached_result
        
        # 从Neo4j获取
        neo4j_pool = await get_neo4j_pool()
        
        # 获取表结构数据
        tables_query = """
        MATCH (t:Table {connection_id: $connection_id})
        RETURN t.id AS id, t.name AS name, t.description AS description
        """
        
        tables_data = await neo4j_pool.execute_read_query(
            tables_query, 
            {'connection_id': connection_id}
        )
        
        # 构建结果
        result = {
            'tables': tables_data,
            'columns': [],
            'relationships': []
        }
        
        # 缓存结果
        self.base_cache.set(cache_key, result, self.schema_cache_ttl)
        
        return result
    
    async def get_qa_pairs_cached(self, connection_id: int, table_names: List[str], top_k: int = 20) -> List[Dict]:
        """获取缓存的问答对"""
        cache_key = f"qa_pairs:{connection_id}:{':'.join(sorted(table_names))}"
        
        # 尝试从缓存获取
        cached_result = self.base_cache.get(cache_key)
        if cached_result:
            logger.debug(f"问答对缓存命中: {connection_id}")
            return cached_result[:top_k]
        
        # 从Neo4j获取
        neo4j_pool = await get_neo4j_pool()
        
        qa_query = """
        MATCH (qa:QAPair)-[:USES_TABLES]->(t:Table)
        WHERE t.name IN $table_names AND qa.connection_id = $connection_id
        WITH qa, count(t) as table_overlap, collect(t.name) as used_tables
        ORDER BY table_overlap DESC, qa.success_rate DESC
        LIMIT $top_k
        RETURN qa, table_overlap, used_tables
        """
        
        qa_data = await neo4j_pool.execute_read_query(
            qa_query,
            {
                'table_names': table_names,
                'connection_id': connection_id,
                'top_k': top_k * 2  # 获取更多数据用于缓存
            }
        )
        
        # 缓存结果
        self.base_cache.set(cache_key, qa_data, self.qa_cache_ttl)
        
        return qa_data[:top_k]
    
    async def preload_connection_data(self, connection_id: int):
        """预加载连接相关数据"""
        logger.info(f"开始预加载连接 {connection_id} 的数据")
        
        try:
            neo4j_pool = await get_neo4j_pool()
            
            # 预加载表结构
            tables_query = """
            MATCH (t:Table {connection_id: $connection_id})
            OPTIONAL MATCH (t)-[:HAS_COLUMN]->(c:Column)
            OPTIONAL MATCH (t1:Table)-[:HAS_COLUMN]->(c1:Column)-[:REFERENCES]->(c2:Column)<-[:HAS_COLUMN]-(t2:Table)
            WHERE t1.connection_id = $connection_id AND t2.connection_id = $connection_id
            RETURN t, collect(DISTINCT c) as columns, collect(DISTINCT {source: t1.name, target: t2.name}) as relationships
            """
            
            preload_data = await neo4j_pool.execute_read_query(
                tables_query,
                {'connection_id': connection_id}
            )
            
            # 缓存预加载数据
            preload_key = f"preload:{connection_id}"
            self.preload_cache[preload_key] = {
                'data': preload_data,
                'timestamp': time.time()
            }
            
            logger.info(f"连接 {connection_id} 数据预加载完成")
            
        except Exception as e:
            logger.error(f"预加载连接 {connection_id} 数据失败: {str(e)}")
    
    def invalidate_connection_cache(self, connection_id: int):
        """失效指定连接的所有缓存"""
        patterns = [
            f"schema_context:{connection_id}:",
            f"qa_pairs:{connection_id}:",
            f"preload:{connection_id}"
        ]
        
        deleted_count = 0
        for pattern in patterns:
            # 删除内存缓存
            keys_to_delete = [
                key for key in self.base_cache._memory_cache.keys()
                if key.startswith(pattern)
            ]
            
            for key in keys_to_delete:
                self.base_cache.delete(key)
                deleted_count += 1
            
            # 删除预加载缓存
            if pattern in self.preload_cache:
                del self.preload_cache[pattern]
                deleted_count += 1
        
        logger.info(f"已失效连接 {connection_id} 的 {deleted_count} 个缓存项")
        return deleted_count
    
    async def warm_up_cache(self, connection_ids: List[int] = None):
        """缓存预热"""
        if not connection_ids:
            # 获取所有活跃连接ID
            from app.db.session import SessionLocal
            from app import crud
            
            db = SessionLocal()
            try:
                connections = crud.db_connection.get_multi(db)
                connection_ids = [conn.id for conn in connections]
            finally:
                db.close()
        
        # 并行预加载
        tasks = [
            self.preload_connection_data(conn_id) 
            for conn_id in connection_ids
        ]
        
        await asyncio.gather(*tasks, return_exceptions=True)
        logger.info(f"缓存预热完成，处理了 {len(connection_ids)} 个连接")


# 全局增强缓存实例
enhanced_cache = EnhancedCacheService()


def neo4j_cached(ttl: Optional[int] = None):
    """Neo4j查询缓存装饰器"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 生成缓存键
            cache_key = f"neo4j:{func.__name__}:{hashlib.md5(str(args + tuple(kwargs.items())).encode()).hexdigest()}"
            
            # 尝试从缓存获取
            cached_result = enhanced_cache.base_cache.get(cache_key)
            if cached_result is not None:
                logger.debug(f"Neo4j查询缓存命中: {func.__name__}")
                return cached_result
            
            # 执行函数并缓存结果
            result = await func(*args, **kwargs)
            enhanced_cache.base_cache.set(cache_key, result, ttl or enhanced_cache.schema_cache_ttl)
            
            return result
        return wrapper
    return decorator
