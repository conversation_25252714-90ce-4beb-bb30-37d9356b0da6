#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import json

class FinancialDataAIAssistant:
    """
    模拟AI模型如何使用元数据系统理解和查询财务数据
    """
    
    def __init__(self, db_path='fin_data.db'):
        self.db_path = db_path
        self.metadata_cache = {}
        self.business_rules = {}
        self._load_metadata()
    
    def _load_metadata(self):
        """加载元数据到缓存"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 加载表描述
            cursor.execute('SELECT * FROM table_descriptions WHERE table_name = "financial_data"')
            table_info = cursor.fetchone()
            if table_info:
                self.metadata_cache['table_info'] = {
                    'name': table_info[0],
                    'description': table_info[1],
                    'business_purpose': table_info[2],
                    'data_scale': table_info[3]
                }
            
            # 加载字段描述
            cursor.execute('''
                SELECT column_name, chinese_name, description, data_type, 
                       business_rules, ai_understanding_points
                FROM column_descriptions 
                WHERE table_name = "financial_data"
            ''')
            columns = cursor.fetchall()
            self.metadata_cache['columns'] = {}
            for col in columns:
                self.metadata_cache['columns'][col[0]] = {
                    'chinese_name': col[1],
                    'description': col[2],
                    'data_type': col[3],
                    'business_rules': col[4],
                    'ai_understanding_points': col[5]
                }
            
            # 加载业务规则
            cursor.execute('''
                SELECT rule_category, rule_description, sql_example, importance_level
                FROM business_rules 
                WHERE table_name = "financial_data"
            ''')
            rules = cursor.fetchall()
            for rule in rules:
                category = rule[0]
                if category not in self.business_rules:
                    self.business_rules[category] = []
                self.business_rules[category].append({
                    'description': rule[1],
                    'sql_example': rule[2],
                    'importance': rule[3]
                })
            
            conn.close()
            print("✅ 元数据加载完成")
            
        except Exception as e:
            print(f"❌ 加载元数据失败: {e}")
    
    def understand_query_intent(self, user_query):
        """理解用户查询意图"""
        print(f"\n🤖 AI分析用户查询: '{user_query}'")
        
        # 模拟AI理解过程
        intent = {}
        
        # 识别查询类型
        if any(word in user_query.lower() for word in ['收入', '营业收入', '主营业务收入']):
            intent['type'] = 'revenue'
            intent['account_pattern'] = '60%'
            intent['amount_field'] = 'credit_amount'
            print("📊 识别为收入类查询")
        elif any(word in user_query.lower() for word in ['资产', '银行存款', '应收账款']):
            intent['type'] = 'asset'
            intent['account_pattern'] = '1%'
            intent['amount_field'] = 'balance'
            print("💰 识别为资产类查询")
        elif any(word in user_query.lower() for word in ['费用', '成本', '管理费用', '销售费用']):
            intent['type'] = 'expense'
            intent['account_pattern'] = ['64%', '66%']
            intent['amount_field'] = 'debit_amount'
            print("💸 识别为费用类查询")
        
        # 识别时间范围
        if '2024' in user_query:
            intent['year'] = 2024
        if '9月' in user_query or '九月' in user_query:
            intent['month'] = 9
        
        return intent
    
    def get_field_guidance(self, intent):
        """根据查询意图获取字段使用指导"""
        print(f"\n📋 获取字段使用指导...")
        
        field_name = intent.get('amount_field')
        if field_name and field_name in self.metadata_cache['columns']:
            field_info = self.metadata_cache['columns'][field_name]
            print(f"✅ 字段: {field_name} ({field_info['chinese_name']})")
            print(f"📝 描述: {field_info['description']}")
            print(f"🧠 AI理解要点: {field_info['ai_understanding_points']}")
            
            # 获取相关业务规则
            if intent['type'] in ['revenue', 'asset', 'expense']:
                rules = self.business_rules.get('科目分类与金额字段对应', [])
                for rule in rules:
                    if intent['type'] == 'revenue' and 'credit_amount' in rule['description']:
                        print(f"⚠️  关键规则: {rule['description']}")
                    elif intent['type'] == 'asset' and 'balance' in rule['description']:
                        print(f"⚠️  关键规则: {rule['description']}")
                    elif intent['type'] == 'expense' and 'debit_amount' in rule['description']:
                        print(f"⚠️  关键规则: {rule['description']}")
    
    def generate_sql(self, intent):
        """基于元数据生成SQL查询"""
        print(f"\n🔧 生成SQL查询...")
        
        # 基础SELECT子句
        amount_field = intent['amount_field']
        
        # 处理balance字段的特殊类型转换
        if amount_field == 'balance':
            amount_expr = f"SUM(CAST({amount_field} AS REAL))"
            print("🔄 应用balance字段类型转换规则")
        else:
            amount_expr = f"SUM({amount_field})"
        
        # 构建SQL
        sql_parts = [
            "SELECT",
            "    accounting_unit_name as '核算单位',",
            f"    {amount_expr} as '{self._get_amount_label(intent['type'])}'",
            "FROM financial_data",
            "WHERE 1=1"
        ]
        
        # 添加科目条件
        if isinstance(intent['account_pattern'], list):
            conditions = " OR ".join([f"account_code LIKE '{pattern}'" for pattern in intent['account_pattern']])
            sql_parts.append(f"    AND ({conditions})")
        else:
            sql_parts.append(f"    AND account_code LIKE '{intent['account_pattern']}'")
        
        # 添加时间条件
        if 'year' in intent:
            sql_parts.append(f"    AND year = {intent['year']}")
        if 'month' in intent:
            sql_parts.append(f"    AND month = {intent['month']}")
        
        # 添加GROUP BY和ORDER BY
        sql_parts.extend([
            "GROUP BY accounting_unit_name",
            f"ORDER BY {amount_expr} DESC"
        ])
        
        sql = "\n".join(sql_parts) + ";"
        print(f"✅ 生成的SQL:\n{sql}")
        return sql
    
    def _get_amount_label(self, query_type):
        """获取金额字段的中文标签"""
        labels = {
            'revenue': '收入金额',
            'asset': '资产余额',
            'expense': '费用金额'
        }
        return labels.get(query_type, '金额')
    
    def execute_query(self, sql):
        """执行查询并返回结果"""
        print(f"\n💾 执行查询...")
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute(sql)
            results = cursor.fetchall()
            
            # 获取列名
            column_names = [description[0] for description in cursor.description]
            
            conn.close()
            
            print(f"✅ 查询成功，返回 {len(results)} 条记录")
            return column_names, results
            
        except Exception as e:
            print(f"❌ 查询执行失败: {e}")
            return None, None
    
    def format_results(self, column_names, results, limit=5):
        """格式化查询结果"""
        if not results:
            return "没有找到匹配的数据"
        
        print(f"\n📊 查询结果 (显示前{limit}条):")
        print("-" * 60)
        
        # 打印表头
        header = " | ".join([f"{name:>15}" for name in column_names])
        print(header)
        print("-" * len(header))
        
        # 打印数据行
        for i, row in enumerate(results[:limit]):
            formatted_row = []
            for j, value in enumerate(row):
                if isinstance(value, (int, float)) and j > 0:  # 金额字段格式化
                    formatted_row.append(f"{value:>15,.2f}")
                else:
                    formatted_row.append(f"{str(value):>15}")
            print(" | ".join(formatted_row))
        
        if len(results) > limit:
            print(f"... 还有 {len(results) - limit} 条记录")
        
        # 计算总计
        if len(column_names) > 1 and results:
            total = sum(row[1] for row in results if isinstance(row[1], (int, float)))
            print("-" * 60)
            print(f"{'总计':>15} | {total:>15,.2f}")
    
    def process_query(self, user_query):
        """处理完整的查询流程"""
        print("=" * 80)
        print(f"🎯 AI财务数据查询助手")
        print("=" * 80)
        
        # 1. 理解查询意图
        intent = self.understand_query_intent(user_query)
        
        if not intent:
            print("❌ 无法理解查询意图")
            return
        
        # 2. 获取字段指导
        self.get_field_guidance(intent)
        
        # 3. 生成SQL
        sql = self.generate_sql(intent)
        
        # 4. 执行查询
        column_names, results = self.execute_query(sql)
        
        # 5. 格式化结果
        if results is not None:
            self.format_results(column_names, results)
        
        print("\n" + "=" * 80)
        print("🎉 查询完成！")

def main():
    """主函数 - 演示AI模型使用元数据系统"""
    
    # 创建AI助手实例
    ai_assistant = FinancialDataAIAssistant()
    
    # 测试查询示例
    test_queries = [
        "查询2024年9月的收入情况",
        "显示2024年9月的资产余额",
        "分析2024年9月的费用支出"
    ]
    
    for query in test_queries:
        ai_assistant.process_query(query)
        print("\n" + "🔄 " * 20 + "\n")

if __name__ == '__main__':
    main()
