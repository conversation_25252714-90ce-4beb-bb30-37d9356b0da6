#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证借方金额和贷方金额的数据情况
"""

import sqlite3
import os

def verify_debit_credit_amounts():
    """验证借方金额和贷方金额的数据"""
    
    db_path = "fin_data.db"
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("=== 验证借方金额和贷方金额数据 ===\n")
        
        # 1. 检查管理费用记录的借方和贷方金额分布
        print("1. 检查2024年3月管理费用的借方和贷方金额分布:")
        cursor.execute("""
            SELECT 
                COUNT(*) as total_records,
                COUNT(CASE WHEN debit_amount > 0 THEN 1 END) as debit_positive_count,
                COUNT(CASE WHEN credit_amount > 0 THEN 1 END) as credit_positive_count,
                COUNT(CASE WHEN debit_amount = 0 THEN 1 END) as debit_zero_count,
                COUNT(CASE WHEN credit_amount = 0 THEN 1 END) as credit_zero_count,
                SUM(debit_amount) as total_debit,
                SUM(credit_amount) as total_credit
            FROM financial_data 
            WHERE year = 2024 AND month = 3 AND account_full_name LIKE '%管理费用%'
        """)
        stats = cursor.fetchone()
        print(f"   总记录数: {stats[0]}")
        print(f"   借方金额 > 0 的记录数: {stats[1]}")
        print(f"   贷方金额 > 0 的记录数: {stats[2]}")
        print(f"   借方金额 = 0 的记录数: {stats[3]}")
        print(f"   贷方金额 = 0 的记录数: {stats[4]}")
        print(f"   借方金额总和: {stats[5]:,.2f}")
        print(f"   贷方金额总和: {stats[6]:,.2f}")
        
        # 2. 对比使用借方金额和贷方金额的查询结果
        print(f"\n2. 对比使用借方金额和贷方金额的查询结果:")
        
        # 使用借方金额的查询
        print(f"\n   使用借方金额(debit_amount)的查询:")
        cursor.execute("""
            SELECT 
                accounting_unit_name, 
                SUM(debit_amount) AS total_management_expense
            FROM financial_data
            WHERE year = 2024 AND month = 3 AND account_full_name LIKE '%管理费用%'
            GROUP BY accounting_unit_name
            ORDER BY total_management_expense DESC
            LIMIT 5
        """)
        debit_results = cursor.fetchall()
        for unit_name, amount in debit_results:
            print(f"     {unit_name}: {amount:,.2f}")
        
        # 使用贷方金额的查询
        print(f"\n   使用贷方金额(credit_amount)的查询:")
        cursor.execute("""
            SELECT 
                accounting_unit_name, 
                SUM(credit_amount) AS total_management_expense
            FROM financial_data
            WHERE year = 2024 AND month = 3 AND account_full_name LIKE '%管理费用%'
            GROUP BY accounting_unit_name
            ORDER BY total_management_expense DESC
            LIMIT 5
        """)
        credit_results = cursor.fetchall()
        for unit_name, amount in credit_results:
            print(f"     {unit_name}: {amount:,.2f}")
        
        # 3. 查看具体的管理费用记录样本
        print(f"\n3. 查看具体的管理费用记录样本:")
        cursor.execute("""
            SELECT 
                accounting_unit_name,
                account_full_name,
                debit_amount,
                credit_amount,
                balance
            FROM financial_data
            WHERE year = 2024 AND month = 3 AND account_full_name LIKE '%管理费用%'
            AND (debit_amount != 0 OR credit_amount != 0)
            ORDER BY ABS(debit_amount) + ABS(credit_amount) DESC
            LIMIT 10
        """)
        sample_records = cursor.fetchall()
        print("   样本记录（按金额大小排序）:")
        print("   " + "-" * 120)
        print(f"   {'会计单位':<25} {'科目名称':<25} {'借方金额':<15} {'贷方金额':<15} {'余额':<15}")
        print("   " + "-" * 120)
        for record in sample_records:
            unit_name = record[0][:24] if record[0] else "N/A"
            account_name = record[1][:24] if record[1] else "N/A"
            debit = float(record[2]) if record[2] is not None else 0.0
            credit = float(record[3]) if record[3] is not None else 0.0
            balance = float(record[4]) if record[4] is not None else 0.0
            print(f"   {unit_name:<25} {account_name:<25} {debit:>12,.2f} {credit:>12,.2f} {balance:>12,.2f}")
        
        # 4. 检查科目性质
        print(f"\n4. 检查管理费用科目的详细信息:")
        cursor.execute("""
            SELECT DISTINCT account_full_name
            FROM financial_data
            WHERE year = 2024 AND month = 3 AND account_full_name LIKE '%管理费用%'
            ORDER BY account_full_name
            LIMIT 10
        """)
        account_names = cursor.fetchall()
        print("   管理费用相关科目（前10个）:")
        for account in account_names:
            print(f"     {account[0]}")
        
        # 5. 分析哪个字段更适合表示管理费用
        print(f"\n5. 分析结论:")
        cursor.execute("""
            SELECT 
                SUM(CASE WHEN debit_amount > 0 THEN debit_amount ELSE 0 END) as positive_debit_sum,
                SUM(CASE WHEN credit_amount > 0 THEN credit_amount ELSE 0 END) as positive_credit_sum,
                COUNT(CASE WHEN debit_amount > 0 THEN 1 END) as positive_debit_count,
                COUNT(CASE WHEN credit_amount > 0 THEN 1 END) as positive_credit_count
            FROM financial_data
            WHERE year = 2024 AND month = 3 AND account_full_name LIKE '%管理费用%'
        """)
        analysis = cursor.fetchone()
        
        print(f"   正值借方金额总和: {analysis[0]:,.2f} (记录数: {analysis[2]})")
        print(f"   正值贷方金额总和: {analysis[1]:,.2f} (记录数: {analysis[3]})")
        
        if analysis[0] > analysis[1]:
            print(f"   ✅ 建议使用 debit_amount (借方金额)，因为管理费用是费用类科目，通常记录在借方")
        else:
            print(f"   ⚠️  贷方金额更大，可能存在冲销或特殊业务处理")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 验证过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    verify_debit_credit_amounts()
