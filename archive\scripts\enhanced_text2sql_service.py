#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
增强的Text2SQL服务 - 集成元数据系统
这个文件展示了如何修改现有的text2sql_service.py来集成我们的元数据系统
"""

import sqlite3
from typing import Dict, Any, List, Optional
from sqlalchemy.orm import Session
from app.core.config import settings

def get_financial_metadata(connection_path: str, table_name: str = "financial_data") -> Dict[str, Any]:
    """
    获取财务表的元数据信息
    
    Args:
        connection_path: 数据库连接路径
        table_name: 表名，默认为financial_data
    
    Returns:
        包含表描述、字段描述和业务规则的字典
    """
    try:
        conn = sqlite3.connect(connection_path)
        cursor = conn.cursor()
        
        metadata = {
            "table_description": None,
            "column_descriptions": [],
            "business_rules": [],
            "has_metadata": False
        }
        
        # 检查是否存在元数据表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='table_descriptions'")
        if not cursor.fetchone():
            print("⚠️  未找到元数据表，使用基础模式")
            conn.close()
            return metadata
        
        # 查询表描述
        cursor.execute("""
            SELECT description, business_purpose, data_scale 
            FROM table_descriptions 
            WHERE table_name = ?
        """, (table_name,))
        
        table_desc = cursor.fetchone()
        if table_desc:
            metadata["table_description"] = {
                "description": table_desc[0],
                "business_purpose": table_desc[1],
                "data_scale": table_desc[2]
            }
        
        # 查询字段描述
        cursor.execute("""
            SELECT column_name, chinese_name, description, data_type, 
                   business_rules, ai_understanding_points
            FROM column_descriptions 
            WHERE table_name = ?
            ORDER BY column_name
        """, (table_name,))
        
        column_descs = cursor.fetchall()
        for col in column_descs:
            metadata["column_descriptions"].append({
                "column_name": col[0],
                "chinese_name": col[1],
                "description": col[2],
                "data_type": col[3],
                "business_rules": col[4],
                "ai_understanding_points": col[5]
            })
        
        # 查询关键业务规则
        cursor.execute("""
            SELECT rule_category, rule_description, sql_example, importance_level
            FROM business_rules 
            WHERE table_name = ?
            ORDER BY 
                CASE importance_level 
                    WHEN 'CRITICAL' THEN 1 
                    WHEN 'HIGH' THEN 2 
                    WHEN 'MEDIUM' THEN 3 
                    ELSE 4 
                END,
                rule_category
        """, (table_name,))
        
        business_rules = cursor.fetchall()
        for rule in business_rules:
            metadata["business_rules"].append({
                "category": rule[0],
                "description": rule[1],
                "sql_example": rule[2],
                "importance": rule[3]
            })
        
        metadata["has_metadata"] = True
        conn.close()
        
        print(f"✅ 成功加载元数据: {len(metadata['column_descriptions'])}个字段, {len(metadata['business_rules'])}个规则")
        return metadata
        
    except Exception as e:
        print(f"⚠️  获取元数据失败: {e}")
        return {
            "table_description": None,
            "column_descriptions": [],
            "business_rules": [],
            "has_metadata": False
        }

def construct_enhanced_prompt(schema_context: Dict[str, Any], query: str, 
                            value_mappings: Dict[str, Dict[str, str]], 
                            metadata: Dict[str, Any]) -> str:
    """
    构建包含元数据的增强Prompt
    
    Args:
        schema_context: 表结构上下文
        query: 自然语言查询
        value_mappings: 值映射
        metadata: 元数据信息
    
    Returns:
        增强的prompt字符串
    """
    
    # 基础表结构信息
    schema_str = format_schema_for_prompt(schema_context)
    
    # 值映射信息
    mappings_str = ""
    if value_mappings:
        mappings_str = "-- 值映射:\n"
        for column, mappings in value_mappings.items():
            mappings_str += f"-- 对于 {column}:\n"
            for nl_term, db_value in mappings.items():
                mappings_str += f"--   自然语言中的'{nl_term}'指数据库中的'{db_value}'\n"
        mappings_str += "\n"
    
    # 构建元数据增强部分
    metadata_str = ""
    if metadata.get("has_metadata"):
        metadata_str = "\n### 🎯 财务业务元数据 (重要!)\n\n"
        
        # 表描述
        if metadata.get("table_description"):
            table_desc = metadata["table_description"]
            metadata_str += f"**表说明**: {table_desc['description']}\n"
            metadata_str += f"**业务用途**: {table_desc['business_purpose']}\n"
            metadata_str += f"**数据规模**: {table_desc['data_scale']}\n\n"
        
        # 关键业务规则
        if metadata.get("business_rules"):
            metadata_str += "### ⚠️ 关键业务规则 (必须遵守!):\n\n"
            for rule in metadata["business_rules"]:
                if rule["importance"] in ["CRITICAL", "HIGH"]:
                    metadata_str += f"**【{rule['importance']}】{rule['category']}**:\n"
                    metadata_str += f"- {rule['description']}\n"
                    if rule["sql_example"]:
                        metadata_str += f"- 示例: `{rule['sql_example']}`\n"
                    metadata_str += "\n"
        
        # 字段详细说明
        if metadata.get("column_descriptions"):
            metadata_str += "### 📋 字段详细说明:\n\n"
            
            # 按类别组织字段
            field_categories = {
                "金额字段": [],
                "科目字段": [],
                "时间字段": [],
                "其他字段": []
            }
            
            for col in metadata["column_descriptions"]:
                col_name = col["column_name"]
                chinese_name = col["chinese_name"]
                ai_points = col["ai_understanding_points"]
                
                field_info = f"- **{col_name}** ({chinese_name}): {ai_points}"
                
                if "amount" in col_name or col_name == "balance":
                    field_categories["金额字段"].append(field_info)
                elif "account" in col_name:
                    field_categories["科目字段"].append(field_info)
                elif col_name in ["year", "month"]:
                    field_categories["时间字段"].append(field_info)
                else:
                    field_categories["其他字段"].append(field_info)
            
            # 输出分类字段
            for category, fields in field_categories.items():
                if fields:
                    metadata_str += f"**{category}**:\n"
                    for field in fields[:5]:  # 限制显示数量
                        metadata_str += f"{field}\n"
                    if len(fields) > 5:
                        metadata_str += f"... 还有{len(fields)-5}个字段\n"
                    metadata_str += "\n"
    
    # 构建完整prompt
    prompt = f"""
你是一名专业的SQL开发专家，专门将自然语言问题转换为精确的SQL查询。

### 📊 数据库结构:
{schema_str}

{mappings_str}

{metadata_str}

### 🎯 用户查询:
{query}

### 📝 生成要求:

1. **严格遵循业务规则**: 特别注意不同科目类型使用不同的金额字段
2. **正确的字段选择**: 
   - 资产负债类科目 (1xxx, 2xxx, 3xxx) → 使用 `balance` 字段
   - 收入类科目 (60xx) → 使用 `credit_amount` 字段  
   - 成本费用类科目 (64xx, 66xx) → 使用 `debit_amount` 字段
3. **数据类型处理**: balance字段为TEXT类型，需要 `CAST(balance AS REAL)` 转换
4. **科目识别**: 根据科目编号自动识别科目类别并选择正确字段
5. **标准SQL语法**: 生成有效的SELECT语句

请生成准确的SQL查询:
"""
    
    return prompt

def format_schema_for_prompt(schema_context: Dict[str, Any]) -> str:
    """格式化表结构信息用于prompt"""
    if not schema_context or not schema_context.get("tables"):
        return "-- 未找到相关表结构"
    
    schema_lines = []
    for table in schema_context["tables"]:
        table_name = table.get("name", "unknown")
        table_desc = table.get("description", "")
        
        schema_lines.append(f"-- 表: {table_name}")
        if table_desc:
            schema_lines.append(f"-- 描述: {table_desc}")
        
        columns = table.get("columns", [])
        if columns:
            schema_lines.append(f"CREATE TABLE {table_name} (")
            for i, col in enumerate(columns):
                col_name = col.get("column_name", "unknown")
                col_type = col.get("data_type", "TEXT")
                col_desc = col.get("description", "")
                
                line = f"    {col_name} {col_type}"
                if col_desc:
                    line += f" -- {col_desc}"
                if i < len(columns) - 1:
                    line += ","
                schema_lines.append(line)
            schema_lines.append(");")
        schema_lines.append("")
    
    return "\n".join(schema_lines)

def enhanced_process_text2sql_query(db: Session, connection, natural_language_query: str):
    """
    增强的Text2SQL查询处理函数
    集成了元数据系统的查询处理
    """
    try:
        print(f"🔍 处理查询: {natural_language_query}")
        
        # 1. 检索相关表结构 (保持原有逻辑)
        from app.services.text2sql_utils import retrieve_relevant_schema
        schema_context = retrieve_relevant_schema(db, connection.id, natural_language_query)
        
        if not schema_context["tables"]:
            return {
                "sql": "",
                "results": None,
                "error": "无法为此查询识别相关表。",
                "context": {"schema_context": schema_context}
            }
        
        # 2. 获取值映射 (保持原有逻辑)
        from app.services.text2sql_utils import get_value_mappings
        value_mappings = get_value_mappings(db, schema_context)
        
        # 3. 🆕 获取财务元数据
        metadata = get_financial_metadata(connection.database_name)
        
        # 4. 🆕 构建增强prompt
        prompt = construct_enhanced_prompt(schema_context, natural_language_query, value_mappings, metadata)
        
        # 5. 调用LLM API (保持原有逻辑)
        from app.services.text2sql_service import call_llm_api
        llm_response = call_llm_api(prompt)
        
        # 6. 提取和处理SQL (保持原有逻辑)
        from app.services.text2sql_utils import extract_sql_from_llm_response, process_sql_with_value_mappings, validate_sql
        sql = extract_sql_from_llm_response(llm_response)
        processed_sql = process_sql_with_value_mappings(sql, value_mappings)
        
        # 7. 验证SQL
        if not validate_sql(processed_sql):
            return {
                "sql": processed_sql,
                "results": None,
                "error": "生成的SQL验证失败。",
                "context": {
                    "schema_context": schema_context,
                    "metadata": metadata,
                    "prompt": prompt,
                    "llm_response": llm_response
                }
            }
        
        # 8. 执行SQL查询 (保持原有逻辑)
        from app.services.db_service import get_db_engine
        engine = get_db_engine(connection)
        
        with engine.connect() as conn:
            result = conn.execute(processed_sql)
            rows = result.fetchall()
            columns = list(result.keys())
        
        # 格式化结果
        results = []
        for row in rows:
            results.append(dict(zip(columns, row)))
        
        print(f"✅ 查询成功，返回 {len(results)} 条记录")
        
        return {
            "sql": processed_sql,
            "results": results,
            "error": None,
            "context": {
                "schema_context": schema_context,
                "metadata": metadata,
                "enhanced": True
            }
        }
        
    except Exception as e:
        print(f"❌ 查询处理失败: {e}")
        return {
            "sql": "",
            "results": None,
            "error": f"查询处理失败: {str(e)}",
            "context": None
        }

# 使用示例
if __name__ == "__main__":
    # 测试元数据获取
    db_path = "fin_data.db"
    metadata = get_financial_metadata(db_path)
    
    print("📊 元数据测试结果:")
    print(f"表描述: {metadata.get('table_description', {}).get('description', 'N/A')}")
    print(f"字段数量: {len(metadata.get('column_descriptions', []))}")
    print(f"业务规则数量: {len(metadata.get('business_rules', []))}")
    print(f"元数据可用: {metadata.get('has_metadata', False)}")
