"""
Neo4j连接池优化服务
解决频繁创建连接的性能问题
"""
import asyncio
import logging
from typing import Dict, Any, Optional, List
from contextlib import asynccontextmanager
from neo4j import GraphDatabase, AsyncGraphDatabase
from neo4j.exceptions import ServiceUnavailable

from app.core.config import settings

logger = logging.getLogger(__name__)


class Neo4jConnectionPool:
    """Neo4j连接池管理器"""
    
    def __init__(self):
        self.driver = None
        self._initialized = False
        self._connection_config = {
            'max_connection_lifetime': 3600,  # 1小时
            'max_connection_pool_size': 50,   # 最大连接数
            'connection_acquisition_timeout': 60,  # 获取连接超时
            'keep_alive': True,
            'trust': 'TRUST_ALL_CERTIFICATES'  # 生产环境需要配置证书
        }
    
    async def initialize(self):
        """初始化连接池"""
        if self._initialized:
            return
            
        try:
            self.driver = AsyncGraphDatabase.driver(
                settings.NEO4J_URI,
                auth=(settings.NEO4J_USER, settings.NEO4J_PASSWORD),
                **self._connection_config
            )
            
            # 测试连接
            async with self.driver.session() as session:
                await session.run("RETURN 1")
            
            self._initialized = True
            logger.info("Neo4j连接池初始化成功")
            
        except Exception as e:
            logger.error(f"Neo4j连接池初始化失败: {str(e)}")
            raise
    
    @asynccontextmanager
    async def get_session(self):
        """获取数据库会话的上下文管理器"""
        if not self._initialized:
            await self.initialize()
        
        session = None
        try:
            session = self.driver.session()
            yield session
        except Exception as e:
            logger.error(f"Neo4j会话错误: {str(e)}")
            raise
        finally:
            if session:
                await session.close()
    
    async def execute_read_query(self, query: str, parameters: Dict = None) -> List[Dict]:
        """执行只读查询"""
        async with self.get_session() as session:
            result = await session.run(query, parameters or {})
            return await result.data()
    
    async def execute_write_query(self, query: str, parameters: Dict = None) -> List[Dict]:
        """执行写入查询"""
        async with self.get_session() as session:
            result = await session.run(query, parameters or {})
            return await result.data()
    
    async def close(self):
        """关闭连接池"""
        if self.driver:
            await self.driver.close()
            self._initialized = False
            logger.info("Neo4j连接池已关闭")


# 全局连接池实例
neo4j_pool = Neo4jConnectionPool()


async def get_neo4j_pool() -> Neo4jConnectionPool:
    """获取Neo4j连接池实例"""
    if not neo4j_pool._initialized:
        await neo4j_pool.initialize()
    return neo4j_pool
