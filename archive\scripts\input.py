import sqlite3
import csv
from pathlib import Path


def csv_to_sqlite(csv_file, db_name='fin_data.db', table_name='financial_data'):
    """
    将CSV文件导入SQLite数据库
    :param csv_file: CSV文件路径
    :param db_name: 数据库文件名 (默认: fin_data.db)
    :param table_name: 表名称 (默认: financial_data)
    """
    # 连接数据库 (如果不存在则自动创建)
    conn = sqlite3.connect(db_name)
    cursor = conn.cursor()

    # 读取CSV文件确定列名和数据类型
    with open(csv_file, 'r', encoding='utf-8') as f:
        reader = csv.reader(f)
        headers = next(reader)  # 获取列名
        sample_row = next(reader)  # 获取一行样本数据用于类型推断

    # 清洗列名 (替换空格和特殊字符)
    clean_headers = [header.strip().replace(' ', '_').replace('-', '_').replace('/', '_')
                     .replace('(', '').replace(')', '').replace('%', 'pct')
                     for header in headers]

    # 推断数据类型
    type_map = []
    for value in sample_row:
        value = value.strip()
        if not value:
            col_type = 'TEXT'  # 空值默认为TEXT
        elif value.replace('.', '', 1).isdigit():
            col_type = 'REAL' if '.' in value else 'INTEGER'
        else:
            col_type = 'TEXT'
        type_map.append(col_type)

    # 创建表SQL语句
    columns = ', '.join([f'"{col}" {dtype}' for col, dtype in zip(clean_headers, type_map)])
    create_table_sql = f'CREATE TABLE IF NOT EXISTS {table_name} ({columns})'

    # 执行创建表
    cursor.execute(f"DROP TABLE IF EXISTS {table_name}")  # 删除现有表
    cursor.execute(create_table_sql)

    # 插入数据
    with open(csv_file, 'r', encoding='utf-8') as f:
        reader = csv.reader(f)
        next(reader)  # 跳过标题行
        insert_sql = f'INSERT INTO {table_name} VALUES ({",".join("?" * len(clean_headers))})'
        cursor.executemany(insert_sql, reader)

    # 提交并关闭连接
    conn.commit()
    conn.close()
    print(f"成功创建SQLite数据库: {db_name}")
    print(f"表名: {table_name} | 导入记录数: {cursor.rowcount}")


if __name__ == "__main__":
    # 使用示例 (修改csv_path为你的CSV文件路径)
    csv_path = r'C:\Users\<USER>\Downloads\测试科目余额表.csv'  # 替换为你的CSV文件路径
    if Path(csv_path).exists():
        csv_to_sqlite(csv_path)
    else:
        print(f"错误: 文件 {csv_path} 不存在")