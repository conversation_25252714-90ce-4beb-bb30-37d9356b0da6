#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os

def add_financial_data_descriptions():
    """为financial_data表添加描述信息"""
    
    db_path = 'fin_data.db'
    if not os.path.exists(db_path):
        print(f'数据库文件不存在: {db_path}')
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. 创建表描述元数据表
        print("创建表描述元数据表...")
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS table_descriptions (
                table_name TEXT PRIMARY KEY,
                description TEXT NOT NULL,
                business_purpose TEXT,
                data_scale TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 2. 创建字段描述元数据表
        print("创建字段描述元数据表...")
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS column_descriptions (
                table_name TEXT NOT NULL,
                column_name TEXT NOT NULL,
                chinese_name TEXT,
                description TEXT,
                data_type TEXT,
                business_rules TEXT,
                ai_understanding_points TEXT,
                PRIMARY KEY (table_name, column_name)
            )
        ''')
        
        # 3. 插入表描述
        print("插入financial_data表描述...")
        table_desc = '''
        财务辅助科目余额表，包含了企业财务核算的详细信息。
        该表是企业财务管理系统的核心数据表，涵盖了时间维度、组织架构、会计科目、项目管理、银行信息和金额数据等多个方面。
        '''
        
        business_purpose = '''
        用于企业财务核算、报表编制、财务分析和决策支持。
        支持按时间、组织、项目、科目等多维度进行财务数据分析。
        为资产负债表、利润表、现金流量表等财务报表提供基础数据。
        '''
        
        data_scale = '723,333 行记录，31 个字段，包含整数、实数、文本等多种数据类型'
        
        cursor.execute('''
            INSERT OR REPLACE INTO table_descriptions 
            (table_name, description, business_purpose, data_scale)
            VALUES (?, ?, ?, ?)
        ''', ('financial_data', table_desc.strip(), business_purpose.strip(), data_scale))
        
        # 4. 插入字段描述
        print("插入字段描述...")
        
        # 时间维度字段
        column_descriptions = [
            ('financial_data', 'year', '年', '财务数据所属的会计年度', 'INTEGER', '用于时间序列分析', '用于时间序列分析，按年度统计财务数据'),
            ('financial_data', 'month', '月', '财务数据所属的会计月份', 'INTEGER', '取值范围: 1-12', '与year字段配合，精确定位财务数据的时间点'),
            
            # 组织架构字段
            ('financial_data', 'accounting_organization', '核算组织', '核算组织的代码标识', 'INTEGER', '用于区分不同的核算主体', '用于区分不同的核算主体，进行分组统计'),
            ('financial_data', 'accounting_unit_name', '核算单位名称', '核算单位的完整名称', 'TEXT', '核算主体的具体名称', '核算主体的具体名称，用于报表展示和数据筛选'),
            
            # 会计科目字段
            ('financial_data', 'account_code', '科目编号', '会计科目的数字编码', 'INTEGER', '遵循会计准则的科目编码规则', '会计科目的唯一标识，遵循会计准则的科目编码规则'),
            ('financial_data', 'account_full_name', '科目全称', '会计科目的完整名称', 'TEXT', '科目的详细描述', '科目的详细描述，用于财务报表和分析'),
            ('financial_data', 'account_name', '科目名称', '会计科目的简化名称', 'TEXT', '通常与account_full_name相同或为其简化版本', '科目的简称，通常与account_full_name相同或为其简化版本'),
            ('financial_data', 'account_direction', '科目方向', '会计科目的借贷方向属性', 'TEXT', '取值: "借" 或 "贷"', '决定科目的正常余额方向，借方科目增加记借方，贷方科目增加记贷方'),
            
            # 金额字段（核心财务数据）
            ('financial_data', 'opening_debit_amount', '期初借方金额', '会计期间开始时的借方余额', 'REAL', '期初余额的借方部分', '期初余额的借方部分，用于计算期末余额'),
            ('financial_data', 'opening_credit_amount', '期初贷方金额', '会计期间开始时的贷方余额', 'INTEGER', '期初余额的贷方部分', '期初余额的贷方部分，与期初借方金额配合使用'),
            ('financial_data', 'debit_amount', '借方金额', '当期发生的借方金额', 'REAL', '当期借方发生额', '当期借方发生额，反映当期业务活动对科目的借方影响'),
            ('financial_data', 'credit_amount', '贷方金额', '当期发生的贷方金额', 'REAL', '当期贷方发生额', '当期贷方发生额，反映当期业务活动对科目的贷方影响'),
            ('financial_data', 'debit_cumulative', '借方累计', '从年初到当期的借方累计发生额', 'REAL', '年初至今的借方累计金额', '年初至今的借方累计金额，用于年度分析'),
            ('financial_data', 'credit_cumulative', '贷方累计', '从年初到当期的贷方累计发生额', 'REAL', '年初至今的贷方累计金额', '年初至今的贷方累计金额，用于年度分析'),
            ('financial_data', 'balance', '余额', '科目的期末余额', 'TEXT', '期末余额，可能包含借贷方向信息', '期末余额，可能包含借贷方向信息'),
            
            # 项目管理字段
            ('financial_data', 'project_id', '项目ID', '项目的唯一标识符', 'TEXT', '用于项目维度的财务分析', '用于项目维度的财务分析和核算'),
            ('financial_data', 'project_code', '项目编号', '项目的编码', 'TEXT', '项目的业务编号', '项目的业务编号，便于项目管理'),
            ('financial_data', 'project_name', '项目名称', '项目的名称描述', 'TEXT', '项目的具体名称', '项目的具体名称，用于报表展示'),
            
            # 业务分类字段
            ('financial_data', 'market_nature_id', '市场性质ID', '市场性质的标识符', 'TEXT', '用于按市场性质分类分析', '用于按市场性质分类分析'),
            ('financial_data', 'tax_rate_id', '税率ID', '税率的标识符', 'TEXT', '关联税率信息', '关联税率信息，用于税务分析'),
            ('financial_data', 'tax_rate_name', '税率名称', '税率的名称描述', 'TEXT', '税率的具体名称', '税率的具体名称，如"13%增值税"等'),
            ('financial_data', 'business_format_id', '业态ID', '业务形态的标识符', 'TEXT', '用于按业务形态分类统计', '用于按业务形态分类统计'),
            ('financial_data', 'financial_product_id', '金融产品ID', '金融产品的标识符', 'TEXT', '关联金融产品信息', '关联金融产品信息'),
            ('financial_data', 'long_term_deferred_project_id', '长期待摊项目ID', '长期待摊费用项目的标识符', 'TEXT', '用于长期待摊费用的核算和分摊', '用于长期待摊费用的核算和分摊'),
            ('financial_data', 'property_unit_id', '楼盘房号ID', '房地产项目中具体房号的标识符', 'TEXT', '房地产业务中的具体房产单位标识', '房地产业务中的具体房产单位标识'),
            ('financial_data', 'cash_flow_project_id', '现金流量项目ID', '现金流量表项目的标识符', 'TEXT', '用于现金流量表的编制和分析', '用于现金流量表的编制和分析'),
            ('financial_data', 'municipal_enterprise_unit_id', '市属国企单位ID', '市属国有企业单位的标识符', 'TEXT', '特定于国有企业的分类标识', '特定于国有企业的分类标识'),
            
            # 银行信息字段
            ('financial_data', 'bank_account_id', '银行账号ID', '银行账户的标识符', 'TEXT', '关联具体的银行账户信息', '关联具体的银行账户信息'),
            ('financial_data', 'financial_institution_id', '金融机构ID', '金融机构的标识符', 'INTEGER', '标识具体的金融机构', '标识具体的金融机构'),
            ('financial_data', 'bank_routing_number', '联行号', '银行的联行号码', 'INTEGER', '银行间清算的标识号码', '银行间清算的标识号码'),
            ('financial_data', 'bank_name', '银行名称', '银行的名称', 'TEXT', '具体的银行名称', '具体的银行名称，如"中国工商银行"'),
        ]
        
        for desc in column_descriptions:
            cursor.execute('''
                INSERT OR REPLACE INTO column_descriptions 
                (table_name, column_name, chinese_name, description, data_type, business_rules, ai_understanding_points)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', desc)
        
        # 5. 创建业务规则表
        print("创建业务规则表...")
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS business_rules (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                table_name TEXT NOT NULL,
                rule_category TEXT NOT NULL,
                rule_description TEXT NOT NULL,
                sql_example TEXT,
                importance_level TEXT DEFAULT 'HIGH',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # 插入关键业务规则
        business_rules = [
            ('financial_data', '科目分类与金额字段对应', '资产负债类科目必须使用balance字段进行汇总', 
             'SELECT SUM(CAST(balance AS REAL)) FROM financial_data WHERE account_code LIKE "1%"', 'CRITICAL'),
            ('financial_data', '科目分类与金额字段对应', '收入类科目必须使用credit_amount或credit_cumulative字段', 
             'SELECT SUM(credit_amount) FROM financial_data WHERE account_code LIKE "60%"', 'CRITICAL'),
            ('financial_data', '科目分类与金额字段对应', '成本费用类科目必须使用debit_amount或debit_cumulative字段', 
             'SELECT SUM(debit_amount) FROM financial_data WHERE account_code LIKE "64%" OR account_code LIKE "66%"', 'CRITICAL'),
            ('financial_data', '数据类型转换', 'balance字段为TEXT类型，需要使用CAST(balance AS REAL)进行转换', 
             'SELECT CAST(balance AS REAL) FROM financial_data', 'HIGH'),
            ('financial_data', '科目编号识别', '1xxx=资产类，2xxx=负债类，3xxx=所有者权益类，60xx=收入类，64xx/66xx=成本费用类', 
             'SELECT account_code, account_name FROM financial_data WHERE account_code LIKE "1%"', 'HIGH'),
        ]
        
        for rule in business_rules:
            cursor.execute('''
                INSERT INTO business_rules 
                (table_name, rule_category, rule_description, sql_example, importance_level)
                VALUES (?, ?, ?, ?, ?)
            ''', rule)
        
        conn.commit()
        print("✅ 成功为financial_data表添加了描述信息！")
        
        # 6. 验证结果
        print("\n=== 验证结果 ===")
        cursor.execute('SELECT * FROM table_descriptions WHERE table_name = "financial_data"')
        table_info = cursor.fetchone()
        if table_info:
            print(f"表名: {table_info[0]}")
            print(f"描述: {table_info[1]}")
            print(f"业务用途: {table_info[2]}")
            print(f"数据规模: {table_info[3]}")
        
        cursor.execute('SELECT COUNT(*) FROM column_descriptions WHERE table_name = "financial_data"')
        col_count = cursor.fetchone()[0]
        print(f"\n字段描述数量: {col_count}")
        
        cursor.execute('SELECT COUNT(*) FROM business_rules WHERE table_name = "financial_data"')
        rule_count = cursor.fetchone()[0]
        print(f"业务规则数量: {rule_count}")
        
        conn.close()
        
    except Exception as e:
        print(f'添加描述时出错: {e}')

if __name__ == '__main__':
    add_financial_data_descriptions()
