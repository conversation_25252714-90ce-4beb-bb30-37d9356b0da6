# Archive 目录说明

本目录包含项目开发过程中的历史文件，这些文件已完成其使命但保留作为参考。

## 目录结构

```
archive/
├── reports/          # 过程性分析报告
├── tests/           # 开发测试脚本
├── scripts/         # 开发过程脚本
└── README.md        # 本说明文件
```

## 文件分类

### 📊 Reports (过程性报告)
- `CLEANUP_SUCCESS_REPORT.md` - 数据清理成功报告
- `CRITICAL_DATABASE_ANALYSIS.md` - 关键数据库分析
- `DATABASE_ARCHITECTURE_ANALYSIS.md` - 数据库架构分析
- `DATABASE_DUPLICATION_ANALYSIS_REPORT.md` - 数据库重复分析报告
- `FINAL_INTEGRATION_ANALYSIS_REPORT.md` - 最终集成分析报告
- `FINANCIAL_DATA_DESCRIPTION_REPORT.md` - 财务数据描述报告
- `INTEGRATION_COMPLETION_REPORT.md` - 集成完成报告
- `LLM_METADATA_INTEGRATION_ANALYSIS.md` - LLM元数据集成分析
- `METADATA_ONLY_MIGRATION_PLAN.md` - 元数据迁移计划
- `MIGRATION_SUCCESS_REPORT.md` - 迁移成功报告

### 🧪 Tests (测试脚本)
- `check_db_schema.py` - 数据库模式检查
- `check_resource_db_status.py` - 资源数据库状态检查
- `test_enhanced_metadata.py` - 增强元数据测试
- `test_metadata_integration.py` - 元数据集成测试
- `test_metadata_loading.py` - 元数据加载测试
- `test_metadata_queries.py` - 元数据查询测试
- `verify_complete_setup.py` - 完整设置验证
- `verify_database_status.py` - 数据库状态验证
- `verify_debit_credit_amounts.py` - 借贷金额验证
- `verify_rename_success.py` - 重命名成功验证

### 🔧 Scripts (开发脚本)
- `analyze_sql_issue.py` - SQL问题分析
- `cleanup_duplicate_data.py` - 重复数据清理
- `database_duplication_analysis.py` - 数据库重复分析
- `execute_management_expense_query.py` - 管理费用查询执行
- `migrate_metadata_only.py` - 元数据迁移
- `modify_balance_field_type.py` - 余额字段类型修改
- `metadata_integration_implementation.py` - 元数据集成实现
- `enhanced_text2sql_service.py` - 增强Text2SQL服务
- `multi_database_text2sql_service.py` - 多数据库Text2SQL服务
- `add_financial_data_descriptions.py` - 添加财务数据描述
- `input.py` - 输入处理脚本

## 使用说明

### 何时参考这些文件
1. **调试问题时** - 参考测试脚本了解之前的测试方法
2. **理解历史决策** - 查看分析报告了解设计决策的背景
3. **重现问题** - 使用开发脚本重现或分析特定问题
4. **学习参考** - 了解项目的演进过程

### 注意事项
- ⚠️ 这些脚本可能依赖于特定的数据库状态或文件结构
- ⚠️ 运行前请确保理解脚本的作用和影响
- ⚠️ 建议在测试环境中运行，避免影响生产数据
- ⚠️ 部分脚本可能已过时，需要根据当前环境调整

## 清理历史

**清理时间**: 2025年7月28日
**清理原因**: 项目结构优化，提高可维护性
**总文件数**: 31个文件
**总大小**: 约233KB

这些文件被移动到archive目录而非删除，以保留项目的完整历史记录。

## 维护建议

1. **定期审查** - 每季度审查archive内容，删除确实不再需要的文件
2. **文档更新** - 如果发现archive中的重要信息，考虑整合到主文档中
3. **空间管理** - 如果archive目录过大，可以考虑压缩或外部存储
