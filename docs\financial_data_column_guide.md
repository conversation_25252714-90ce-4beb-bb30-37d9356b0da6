# 财务辅助科目余额表 (main.financial_data) 列名说明

## 概述

本文档详细说明了 `main.financial_data` 表中各列的含义和用途，该表是一个财务辅助科目余额表，包含了企业财务核算的详细信息。表中共有31个字段，涵盖了时间维度、组织架构、会计科目、项目管理、银行信息和金额数据等多个方面。

**数据规模**: 723,333 行记录  
**字段数量**: 31 个字段  
**数据类型**: 包含整数、实数、文本等多种数据类型

---

## 字段详细说明

### 1. 时间维度字段

#### `year` (INTEGER)
- **中文名**: 年
- **说明**: 财务数据所属的会计年度
- **数据类型**: 整数
- **示例值**: 2024
- **AI理解要点**: 用于时间序列分析，按年度统计财务数据

#### `month` (INTEGER)
- **中文名**: 月
- **说明**: 财务数据所属的会计月份
- **数据类型**: 整数
- **取值范围**: 1-12
- **示例值**: 9
- **AI理解要点**: 与year字段配合，精确定位财务数据的时间点

### 2. 组织架构字段

#### `accounting_organization` (INTEGER)
- **中文名**: 核算组织
- **说明**: 核算组织的代码标识
- **数据类型**: 整数
- **示例值**: 101
- **AI理解要点**: 用于区分不同的核算主体，进行分组统计

#### `accounting_unit_name` (TEXT)
- **中文名**: 核算单位名称
- **说明**: 核算单位的完整名称
- **数据类型**: 文本
- **示例值**: "REITs专项计划基金（含项目公司）"
- **AI理解要点**: 核算主体的具体名称，用于报表展示和数据筛选

### 3. 会计科目字段

#### `account_code` (INTEGER)
- **中文名**: 科目编号
- **说明**: 会计科目的数字编码
- **数据类型**: 整数
- **示例值**: 1002
- **AI理解要点**: 会计科目的唯一标识，遵循会计准则的科目编码规则

#### `account_full_name` (TEXT)
- **中文名**: 科目全称
- **说明**: 会计科目的完整名称
- **数据类型**: 文本
- **示例值**: "银行存款"
- **AI理解要点**: 科目的详细描述，用于财务报表和分析

#### `account_name` (TEXT)
- **中文名**: 科目名称
- **说明**: 会计科目的简化名称
- **数据类型**: 文本
- **示例值**: "银行存款"
- **AI理解要点**: 科目的简称，通常与account_full_name相同或为其简化版本

#### `account_direction` (TEXT)
- **中文名**: 科目方向
- **说明**: 会计科目的借贷方向属性
- **数据类型**: 文本
- **取值**: "借" 或 "贷"
- **示例值**: "借"
- **AI理解要点**: 决定科目的正常余额方向，借方科目增加记借方，贷方科目增加记贷方

### 4. 金额字段（核心财务数据）

#### `opening_debit_amount` (REAL)
- **中文名**: 期初借方金额
- **说明**: 会计期间开始时的借方余额
- **数据类型**: 实数
- **示例值**: 0.0
- **AI理解要点**: 期初余额的借方部分，用于计算期末余额

#### `opening_credit_amount` (INTEGER)
- **中文名**: 期初贷方金额
- **说明**: 会计期间开始时的贷方余额
- **数据类型**: 整数
- **示例值**: 0
- **AI理解要点**: 期初余额的贷方部分，与期初借方金额配合使用

#### `debit_amount` (REAL)
- **中文名**: 借方金额
- **说明**: 当期发生的借方金额
- **数据类型**: 实数
- **AI理解要点**: 当期借方发生额，反映当期业务活动对科目的借方影响

#### `credit_amount` (REAL)
- **中文名**: 贷方金额
- **说明**: 当期发生的贷方金额
- **数据类型**: 实数
- **AI理解要点**: 当期贷方发生额，反映当期业务活动对科目的贷方影响

#### `debit_cumulative` (REAL)
- **中文名**: 借方累计
- **说明**: 从年初到当期的借方累计发生额
- **数据类型**: 实数
- **AI理解要点**: 年初至今的借方累计金额，用于年度分析

#### `credit_cumulative` (REAL)
- **中文名**: 贷方累计
- **说明**: 从年初到当期的贷方累计发生额
- **数据类型**: 实数
- **AI理解要点**: 年初至今的贷方累计金额，用于年度分析

#### `balance` (TEXT)
- **中文名**: 余额
- **说明**: 科目的期末余额
- **数据类型**: 文本
- **AI理解要点**: 期末余额，可能包含借贷方向信息

### 5. 项目管理字段

#### `project_id` (TEXT)
- **中文名**: 项目ID
- **说明**: 项目的唯一标识符
- **数据类型**: 文本
- **AI理解要点**: 用于项目维度的财务分析和核算

#### `project_code` (TEXT)
- **中文名**: 项目编号
- **说明**: 项目的编码
- **数据类型**: 文本
- **AI理解要点**: 项目的业务编号，便于项目管理

#### `project_name` (TEXT)
- **中文名**: 项目名称
- **说明**: 项目的名称描述
- **数据类型**: 文本
- **AI理解要点**: 项目的具体名称，用于报表展示

### 6. 业务分类字段

#### `market_nature_id` (TEXT)
- **中文名**: 市场性质ID
- **说明**: 市场性质的标识符
- **数据类型**: 文本
- **AI理解要点**: 用于按市场性质分类分析

#### `tax_rate_id` (TEXT)
- **中文名**: 税率ID
- **说明**: 税率的标识符
- **数据类型**: 文本
- **AI理解要点**: 关联税率信息，用于税务分析

#### `tax_rate_name` (TEXT)
- **中文名**: 税率名称
- **说明**: 税率的名称描述
- **数据类型**: 文本
- **AI理解要点**: 税率的具体名称，如"13%增值税"等

#### `business_format_id` (TEXT)
- **中文名**: 业态ID
- **说明**: 业务形态的标识符
- **数据类型**: 文本
- **AI理解要点**: 用于按业务形态分类统计

#### `financial_product_id` (TEXT)
- **中文名**: 金融产品ID
- **说明**: 金融产品的标识符
- **数据类型**: 文本
- **AI理解要点**: 关联金融产品信息

#### `long_term_deferred_project_id` (TEXT)
- **中文名**: 长期待摊项目ID
- **说明**: 长期待摊费用项目的标识符
- **数据类型**: 文本
- **AI理解要点**: 用于长期待摊费用的核算和分摊

#### `property_unit_id` (TEXT)
- **中文名**: 楼盘房号ID
- **说明**: 房地产项目中具体房号的标识符
- **数据类型**: 文本
- **AI理解要点**: 房地产业务中的具体房产单位标识

#### `cash_flow_project_id` (TEXT)
- **中文名**: 现金流量项目ID
- **说明**: 现金流量表项目的标识符
- **数据类型**: 文本
- **AI理解要点**: 用于现金流量表的编制和分析

#### `municipal_enterprise_unit_id` (TEXT)
- **中文名**: 市属国企单位ID
- **说明**: 市属国有企业单位的标识符
- **数据类型**: 文本
- **AI理解要点**: 特定于国有企业的分类标识

### 7. 银行信息字段

#### `bank_account_id` (TEXT)
- **中文名**: 银行账号ID
- **说明**: 银行账户的标识符
- **数据类型**: 文本
- **AI理解要点**: 关联具体的银行账户信息

#### `financial_institution_id` (INTEGER)
- **中文名**: 金融机构ID
- **说明**: 金融机构的标识符
- **数据类型**: 整数
- **AI理解要点**: 标识具体的金融机构

#### `bank_routing_number` (INTEGER)
- **中文名**: 联行号
- **说明**: 银行的联行号码
- **数据类型**: 整数
- **AI理解要点**: 银行间清算的标识号码

#### `bank_name` (TEXT)
- **中文名**: 银行名称
- **说明**: 银行的名称
- **数据类型**: 文本
- **AI理解要点**: 具体的银行名称，如"中国工商银行"

---

## 会计科目分类与金额字段使用规则

### 重要提示：不同科目类别使用不同的金额字段进行汇总

根据会计准则和业务逻辑，不同类别的会计科目在进行金额汇总时应使用不同的字段：

### 1. 资产负债类科目（余额类科目）
**使用字段**: `balance`

**科目范围**:
- **资产类科目** (1xxx): 如银行存款(1002)、应收账款(1131)、固定资产(1601)等
- **负债类科目** (2xxx): 如应付账款(2202)、短期借款(2001)、应付职工薪酬(2211)等
- **所有者权益类科目** (3xxx): 如实收资本(3001)、资本公积(3002)、盈余公积(3101)等

**查询示例**:
```sql
-- 查询资产类科目余额汇总
SELECT accounting_unit_name, SUM(CAST(balance AS REAL)) as total_balance
FROM financial_data
WHERE account_code LIKE '1%' AND year = 2024 AND month = 12
GROUP BY accounting_unit_name;
```

**AI理解要点**:
- 资产负债类科目关注的是期末余额状态，不是发生额
- balance字段反映了科目的最终财务状况
- 用于编制资产负债表

### 2. 收入类科目
**使用字段**: `credit_amount` 或 `credit_cumulative`

**科目范围**:
- **主营业务收入** (6001): 企业主要经营活动的收入
- **其他业务收入** (6051): 企业非主营业务的收入
- **投资收益** (6111): 投资活动产生的收益
- **营业外收入** (6301): 非经营性收入

**查询示例**:
```sql
-- 查询当期主营业务收入
SELECT accounting_unit_name, SUM(credit_amount) as total_revenue
FROM financial_data
WHERE account_full_name LIKE '%主营业务收入%'
  AND year = 2024 AND month = 9
GROUP BY accounting_unit_name;

-- 查询累计主营业务收入
SELECT accounting_unit_name, SUM(credit_cumulative) as total_revenue_ytd
FROM financial_data
WHERE account_full_name LIKE '%主营业务收入%'
  AND year = 2024 AND month = 9
GROUP BY accounting_unit_name;
```

**AI理解要点**:
- 收入类科目正常余额在贷方，增加时记贷方
- credit_amount反映当期收入发生额
- credit_cumulative反映年初至今累计收入
- 用于编制利润表的收入部分

### 3. 成本费用类科目
**使用字段**: `debit_amount` 或 `debit_cumulative`

**科目范围**:
- **主营业务成本** (6401): 与主营业务收入直接相关的成本
- **管理费用** (6602): 企业管理活动发生的费用
- **销售费用** (6601): 销售活动中发生的费用
- **财务费用** (6603): 筹资活动中发生的费用
- **研发费用** (6604): 研究开发活动的费用
- **营业外支出** (6711): 非经营性支出

**查询示例**:
```sql
-- 查询当期管理费用
SELECT accounting_unit_name, SUM(debit_amount) as total_admin_expense
FROM financial_data
WHERE account_full_name LIKE '%管理费用%'
  AND year = 2024 AND month = 9
GROUP BY accounting_unit_name;

-- 查询累计销售费用
SELECT accounting_unit_name, SUM(debit_cumulative) as total_sales_expense_ytd
FROM financial_data
WHERE account_full_name LIKE '%销售费用%'
  AND year = 2024 AND month = 9
GROUP BY accounting_unit_name;
```

**AI理解要点**:
- 成本费用类科目正常余额在借方，增加时记借方
- debit_amount反映当期成本费用发生额
- debit_cumulative反映年初至今累计成本费用
- 用于编制利润表的成本费用部分

### 4. 科目编号规律识别

**资产类科目**: 1xxx
- 1001-1099: 货币资金类
- 1100-1199: 应收及预付款项类
- 1200-1299: 存货类
- 1400-1499: 长期股权投资类
- 1600-1699: 固定资产类

**负债类科目**: 2xxx
- 2001-2099: 短期借款类
- 2200-2299: 应付及预收款项类
- 2600-2699: 长期借款类

**所有者权益类科目**: 3xxx
- 3001-3099: 实收资本类
- 3100-3199: 资本公积类

**损益类科目**: 6xxx
- 6001-6099: 收入类（使用credit_amount）
- 6400-6799: 成本费用类（使用debit_amount）

### 5. 特殊情况处理

#### 损益结转后的科目
- 如果是年末或期末已结转的损益类科目，可能需要查看balance字段
- 结转前使用对应的发生额字段，结转后使用余额字段

#### 科目方向判断
- 结合`account_direction`字段确认科目的正常余额方向
- 借方科目（account_direction = '借'）：资产、成本、费用类
- 贷方科目（account_direction = '贷'）：负债、所有者权益、收入类

---

## AI分析建议

### 1. 数据分析维度
- **时间维度**: 使用year和month字段进行时间序列分析
- **组织维度**: 按accounting_unit_name进行分组统计
- **科目维度**: 按account_code或account_name进行科目分析
- **项目维度**: 按project相关字段进行项目核算分析

### 2. 关键财务指标计算
- **期末余额** = 期初余额 + 当期借方发生额 - 当期贷方发生额（借方科目）
- **期末余额** = 期初余额 + 当期贷方发生额 - 当期借方发生额（贷方科目）
- **累计发生额分析**: 使用debit_cumulative和credit_cumulative字段

### 3. 常用查询模式

#### 基础查询模式
- **按时间范围查询**: `WHERE year = ? AND month BETWEEN ? AND ?`
- **按核算单位查询**: `WHERE accounting_unit_name = ?`
- **按项目查询**: `WHERE project_name = ?`

#### 按科目类别查询（重要）
- **资产类科目**: `WHERE account_code LIKE '1%'` → 使用 `SUM(CAST(balance AS REAL))`
- **负债类科目**: `WHERE account_code LIKE '2%'` → 使用 `SUM(CAST(balance AS REAL))`
- **所有者权益类**: `WHERE account_code LIKE '3%'` → 使用 `SUM(CAST(balance AS REAL))`
- **收入类科目**: `WHERE account_code LIKE '60%'` → 使用 `SUM(credit_amount)` 或 `SUM(credit_cumulative)`
- **成本费用类**: `WHERE account_code LIKE '64%' OR account_code LIKE '66%'` → 使用 `SUM(debit_amount)` 或 `SUM(debit_cumulative)`

#### 具体业务查询示例
```sql
-- 资产负债表相关查询
SELECT accounting_unit_name, SUM(CAST(balance AS REAL)) as total_assets
FROM financial_data
WHERE account_code LIKE '1%' AND year = 2024 AND month = 12
GROUP BY accounting_unit_name;

-- 利润表收入查询
SELECT accounting_unit_name, SUM(credit_amount) as monthly_revenue
FROM financial_data
WHERE account_code LIKE '60%' AND year = 2024 AND month = 9
GROUP BY accounting_unit_name;

-- 利润表费用查询
SELECT accounting_unit_name, SUM(debit_amount) as monthly_expenses
FROM financial_data
WHERE (account_code LIKE '64%' OR account_code LIKE '66%')
  AND year = 2024 AND month = 9
GROUP BY accounting_unit_name;
```

### 4. 数据质量检查要点

#### 基础平衡检查
- **借贷平衡**: `SUM(debit_amount) = SUM(credit_amount)` (全部科目)
- **累计金额逻辑**: 累计金额应大于等于当期金额
- **科目方向一致性**: account_direction与实际余额方向是否一致

#### 科目类别特定检查
- **资产类科目**: 检查balance字段是否合理，通常应为正值
- **负债类科目**: 检查balance字段，通常应为负值或贷方余额
- **收入类科目**: 检查credit_amount >= 0，收入不应为负
- **费用类科目**: 检查debit_amount >= 0，费用不应为负

#### 业务逻辑检查
```sql
-- 检查收入类科目是否使用了正确的字段
SELECT account_name, SUM(credit_amount) as revenue, SUM(debit_amount) as should_be_zero
FROM financial_data
WHERE account_code LIKE '60%'
GROUP BY account_name
HAVING SUM(debit_amount) > SUM(credit_amount) * 0.1; -- 借方不应超过贷方的10%

-- 检查费用类科目是否使用了正确的字段
SELECT account_name, SUM(debit_amount) as expense, SUM(credit_amount) as should_be_small
FROM financial_data
WHERE account_code LIKE '66%'
GROUP BY account_name
HAVING SUM(credit_amount) > SUM(debit_amount) * 0.1; -- 贷方不应超过借方的10%
```

---

## 注意事项

### 关键业务规则（必须遵守）

1. **科目分类与金额字段对应关系**（最重要）:
   - **资产负债类科目** → 必须使用 `balance` 字段
   - **收入类科目** → 必须使用 `credit_amount` 或 `credit_cumulative` 字段
   - **成本费用类科目** → 必须使用 `debit_amount` 或 `debit_cumulative` 字段
   - **错误使用字段将导致分析结果完全错误**

2. **数据类型转换**:
   - balance字段为TEXT类型，需要使用 `CAST(balance AS REAL)` 进行转换
   - 金额字段混合使用了REAL和INTEGER类型，计算时注意类型转换

3. **科目编号识别规则**:
   - 1xxx = 资产类（使用balance）
   - 2xxx = 负债类（使用balance）
   - 3xxx = 所有者权益类（使用balance）
   - 60xx = 收入类（使用credit_amount/credit_cumulative）
   - 64xx, 66xx = 成本费用类（使用debit_amount/debit_cumulative）

### 技术注意事项

4. **空值处理**: 某些字段可能包含空值，查询时需要适当处理
5. **编码规范**: 科目编号遵循会计准则，不同类别科目有不同的编号规则
6. **业务逻辑**: 借贷方科目的余额计算逻辑不同，需要结合account_direction字段判断
7. **时间维度**: 区分当期发生额（debit_amount/credit_amount）和累计发生额（debit_cumulative/credit_cumulative）

### 常见错误避免

❌ **错误示例**:
```sql
-- 错误：查询收入时使用了balance字段
SELECT SUM(CAST(balance AS REAL)) FROM financial_data WHERE account_name LIKE '%收入%';

-- 错误：查询资产时使用了发生额字段
SELECT SUM(debit_amount) FROM financial_data WHERE account_code LIKE '1%';
```

✅ **正确示例**:
```sql
-- 正确：查询收入使用credit_amount
SELECT SUM(credit_amount) FROM financial_data WHERE account_name LIKE '%收入%';

-- 正确：查询资产使用balance
SELECT SUM(CAST(balance AS REAL)) FROM financial_data WHERE account_code LIKE '1%';
```

---

*本文档基于实际数据库结构生成，用于辅助AI理解财务数据的结构和含义。*
