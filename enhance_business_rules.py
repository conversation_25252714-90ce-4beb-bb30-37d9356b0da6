#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os

def enhance_business_rules():
    """增强业务规则，提供更全面的财务逻辑指导"""
    
    db_path = 'fin_data.db'
    if not os.path.exists(db_path):
        print(f'数据库文件不存在: {db_path}')
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("📋 开始增强业务规则...")
        
        # 新增详细的业务规则
        enhanced_rules = [
            # 科目编号识别规则
            ('financial_data', '科目编号识别规则', 
             '资产类科目编号以1开头(1001-1999)，负债类以2开头(2001-2999)，所有者权益类以3开头(3001-3999)，成本类以4开头(4001-4999)，损益类以5-6开头(5001-6999)',
             'SELECT account_code, account_name FROM financial_data WHERE account_code BETWEEN 1001 AND 1999 LIMIT 5',
             'CRITICAL'),
            
            # 收入科目细分规则
            ('financial_data', '收入科目细分规则',
             '主营业务收入(6001-6099)，其他业务收入(6101-6199)，投资收益(6301-6399)，营业外收入(6701-6799)，所有收入类科目必须使用credit_amount字段',
             'SELECT account_code, account_name, SUM(credit_amount) as 收入金额 FROM financial_data WHERE account_code BETWEEN 6001 AND 6799 GROUP BY account_code, account_name',
             'CRITICAL'),
            
            # 费用科目细分规则  
            ('financial_data', '费用科目细分规则',
             '主营业务成本(6401-6499)，销售费用(6601-6699)，管理费用(6602-6699)，财务费用(6603-6699)，所有费用类科目必须使用debit_amount字段',
             'SELECT account_code, account_name, SUM(debit_amount) as 费用金额 FROM financial_data WHERE account_code BETWEEN 6401 AND 6699 GROUP BY account_code, account_name',
             'CRITICAL'),
            
            # 资产科目规则
            ('financial_data', '资产科目规则',
             '流动资产(1001-1199)，非流动资产(1201-1999)，所有资产类科目必须使用balance字段，且需要CAST转换',
             'SELECT account_code, account_name, SUM(CAST(balance AS REAL)) as 资产余额 FROM financial_data WHERE account_code BETWEEN 1001 AND 1999 GROUP BY account_code, account_name',
             'CRITICAL'),
            
            # 负债科目规则
            ('financial_data', '负债科目规则', 
             '流动负债(2001-2199)，非流动负债(2201-2999)，所有负债类科目必须使用balance字段，且需要CAST转换',
             'SELECT account_code, account_name, SUM(CAST(balance AS REAL)) as 负债余额 FROM financial_data WHERE account_code BETWEEN 2001 AND 2999 GROUP BY account_code, account_name',
             'CRITICAL'),
            
            # 时间筛选规则
            ('financial_data', '时间筛选规则',
             '查询特定时间段时，必须同时使用year和month字段进行筛选，月份范围1-12',
             'SELECT year, month, COUNT(*) as 记录数 FROM financial_data WHERE year = 2024 AND month = 9 GROUP BY year, month',
             'HIGH'),
            
            # 组织筛选规则
            ('financial_data', '组织筛选规则',
             '按组织查询时，可使用accounting_organization(数字编号)或accounting_unit_name(名称)进行筛选',
             'SELECT accounting_organization, accounting_unit_name, COUNT(*) as 记录数 FROM financial_data GROUP BY accounting_organization, accounting_unit_name',
             'HIGH'),
            
            # 金额汇总规则
            ('financial_data', '金额汇总规则',
             '进行金额汇总时，必须根据科目类型选择正确的金额字段：资产负债用balance，收入用credit_amount，费用用debit_amount',
             'SELECT CASE WHEN account_code < 2000 THEN "资产" WHEN account_code < 3000 THEN "负债" WHEN account_code >= 6000 THEN "损益" END as 科目类型, COUNT(*) FROM financial_data GROUP BY 1',
             'CRITICAL'),
            
            # 数据类型处理规则
            ('financial_data', '数据类型处理规则',
             'balance字段为TEXT类型，在进行数值计算时必须使用CAST(balance AS REAL)进行类型转换',
             'SELECT CAST(balance AS REAL) as 数值余额 FROM financial_data WHERE balance IS NOT NULL AND balance != "" LIMIT 5',
             'HIGH'),
            
            # 空值处理规则
            ('financial_data', '空值处理规则',
             '在进行汇总计算时，应考虑NULL值和空字符串的处理，使用COALESCE或WHERE条件过滤',
             'SELECT COUNT(*) as 总记录, COUNT(balance) as 非空余额记录 FROM financial_data',
             'MEDIUM'),
            
            # 财务报表编制规则
            ('financial_data', '财务报表编制规则',
             '编制资产负债表时使用balance字段，编制利润表时收入用credit_amount，费用用debit_amount',
             'SELECT "资产负债表示例" as 报表类型, SUM(CAST(balance AS REAL)) as 金额 FROM financial_data WHERE account_code < 4000',
             'HIGH'),
            
            # 借贷平衡验证规则
            ('financial_data', '借贷平衡验证规则',
             '在会计期间内，借方发生额总和应等于贷方发生额总和，可用于数据完整性验证',
             'SELECT SUM(debit_amount) as 借方总额, SUM(credit_amount) as 贷方总额, SUM(debit_amount) - SUM(credit_amount) as 差额 FROM financial_data',
             'MEDIUM')
        ]
        
        # 插入增强的业务规则
        for rule in enhanced_rules:
            cursor.execute('''
                INSERT OR REPLACE INTO business_rules 
                (table_name, rule_category, rule_description, sql_example, importance_level)
                VALUES (?, ?, ?, ?, ?)
            ''', rule)
        
        print(f"✅ 已添加 {len(enhanced_rules)} 条增强业务规则")
        
        # 插入字段关系数据
        print("📊 添加字段关系数据...")
        field_relationships = [
            ('financial_data', 'account_code', 'account_name', '一对一', '科目编号对应科目名称', 'SELECT account_code, account_name FROM financial_data WHERE account_code = 1001'),
            ('financial_data', 'account_code', 'account_direction', '一对一', '科目编号决定借贷方向', 'SELECT account_code, account_direction FROM financial_data WHERE account_code = 1001'),
            ('financial_data', 'year', 'month', '一对多', '年份包含多个月份', 'SELECT year, month FROM financial_data WHERE year = 2024'),
            ('financial_data', 'accounting_organization', 'accounting_unit_name', '一对一', '组织编号对应组织名称', 'SELECT accounting_organization, accounting_unit_name FROM financial_data WHERE accounting_organization = 101'),
            ('financial_data', 'debit_amount', 'debit_cumulative', '累计关系', '当期借方金额累计为借方累计', 'SELECT debit_amount, debit_cumulative FROM financial_data WHERE debit_amount > 0'),
            ('financial_data', 'credit_amount', 'credit_cumulative', '累计关系', '当期贷方金额累计为贷方累计', 'SELECT credit_amount, credit_cumulative FROM financial_data WHERE credit_amount > 0'),
            ('financial_data', 'debit_amount', 'credit_amount', '借贷关系', '借贷双方记录业务的两个方面', 'SELECT debit_amount, credit_amount FROM financial_data WHERE debit_amount > 0 OR credit_amount > 0'),
        ]
        
        for rel in field_relationships:
            cursor.execute('''
                INSERT OR REPLACE INTO field_relationships 
                (table_name, primary_field, related_field, relationship_type, relationship_description, usage_example)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', rel)
        
        print(f"✅ 已添加 {len(field_relationships)} 条字段关系")
        
        # 插入查询模式
        print("🔍 添加查询模式...")
        query_patterns = [
            ('收入分析模式', '分析企业收入情况的查询模式', 
             '查询收入,收入分析,营业收入,主营业务收入', 
             'SELECT accounting_unit_name, SUM(credit_amount) as 收入金额 FROM financial_data WHERE account_code LIKE "60%" AND year = {year} AND month = {month} GROUP BY accounting_unit_name ORDER BY 收入金额 DESC',
             'account_code,credit_amount,year,month,accounting_unit_name', '收入分析', 'EASY'),
            
            ('费用分析模式', '分析企业费用支出的查询模式',
             '查询费用,费用分析,成本分析,支出分析',
             'SELECT accounting_unit_name, SUM(debit_amount) as 费用金额 FROM financial_data WHERE account_code LIKE "64%" OR account_code LIKE "66%" AND year = {year} AND month = {month} GROUP BY accounting_unit_name ORDER BY 费用金额 DESC',
             'account_code,debit_amount,year,month,accounting_unit_name', '费用分析', 'EASY'),
            
            ('资产负债分析模式', '分析企业资产负债情况的查询模式',
             '资产分析,负债分析,余额分析,资产负债表',
             'SELECT account_name, SUM(CAST(balance AS REAL)) as 余额 FROM financial_data WHERE account_code < 4000 AND year = {year} AND month = {month} GROUP BY account_name ORDER BY 余额 DESC',
             'account_code,balance,account_name,year,month', '资产负债分析', 'MEDIUM'),
            
            ('时间对比分析模式', '不同时间段的财务数据对比分析',
             '同比分析,环比分析,时间对比,趋势分析',
             'SELECT year, month, SUM(credit_amount) as 当期收入 FROM financial_data WHERE account_code LIKE "60%" GROUP BY year, month ORDER BY year, month',
             'year,month,credit_amount,account_code', '时间序列分析', 'MEDIUM'),
            
            ('组织对比分析模式', '不同组织单位的财务数据对比',
             '组织对比,单位对比,组织绩效,单位绩效',
             'SELECT accounting_unit_name, SUM(credit_amount) as 收入, SUM(debit_amount) as 费用 FROM financial_data WHERE year = {year} GROUP BY accounting_unit_name',
             'accounting_unit_name,credit_amount,debit_amount,year', '组织对比分析', 'MEDIUM')
        ]
        
        for pattern in query_patterns:
            cursor.execute('''
                INSERT OR REPLACE INTO query_patterns 
                (pattern_name, pattern_description, natural_language_examples, sql_template, required_fields, business_scenario, difficulty_level)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', pattern)
        
        print(f"✅ 已添加 {len(query_patterns)} 个查询模式")
        
        conn.commit()
        conn.close()
        
        print("🎉 业务规则增强完成！")
        
    except Exception as e:
        print(f"❌ 增强业务规则时出错: {e}")

if __name__ == "__main__":
    enhance_business_rules()
