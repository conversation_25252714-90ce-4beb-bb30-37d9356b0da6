#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
多数据库支持的Text2SQL服务
支持从resource.db获取元数据，从fin_data.db查询业务数据
"""

import sqlite3
import os
from typing import Dict, Any, List, Optional

class MultiDatabaseText2SQLService:
    """多数据库Text2SQL服务"""
    
    def __init__(self):
        # 数据库路径配置
        self.metadata_db_path = r"C:\Users\<USER>\PycharmProjects\智能数据分析系统\resource.db"
        self.business_db_path = r"C:\Users\<USER>\PycharmProjects\智能数据分析系统\fin_data.db"
        
    def get_financial_metadata(self, table_name: str = "financial_data") -> Dict[str, Any]:
        """从元数据库获取财务表的元数据信息"""
        
        try:
            conn = sqlite3.connect(self.metadata_db_path)
            cursor = conn.cursor()
            
            metadata = {
                "table_description": None,
                "column_descriptions": [],
                "business_rules": [],
                "has_metadata": False
            }
            
            # 检查是否存在元数据表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='table_descriptions'")
            if not cursor.fetchone():
                print("⚠️  元数据表不存在于resource.db")
                conn.close()
                return metadata
            
            # 查询表描述
            cursor.execute("""
                SELECT description, business_purpose, data_scale 
                FROM table_descriptions 
                WHERE table_name = ?
            """, (table_name,))
            
            table_desc = cursor.fetchone()
            if table_desc:
                metadata["table_description"] = {
                    "description": table_desc[0],
                    "business_purpose": table_desc[1],
                    "data_scale": table_desc[2]
                }
            
            # 查询字段描述
            cursor.execute("""
                SELECT column_name, chinese_name, description, data_type, 
                       business_rules, ai_understanding_points
                FROM column_descriptions 
                WHERE table_name = ?
                ORDER BY column_name
            """, (table_name,))
            
            column_descs = cursor.fetchall()
            for col in column_descs:
                metadata["column_descriptions"].append({
                    "column_name": col[0],
                    "chinese_name": col[1],
                    "description": col[2],
                    "data_type": col[3],
                    "business_rules": col[4],
                    "ai_understanding_points": col[5]
                })
            
            # 查询关键业务规则
            cursor.execute("""
                SELECT rule_category, rule_description, sql_example, importance_level
                FROM business_rules 
                WHERE table_name = ?
                ORDER BY 
                    CASE importance_level 
                        WHEN 'CRITICAL' THEN 1 
                        WHEN 'HIGH' THEN 2 
                        WHEN 'MEDIUM' THEN 3 
                        ELSE 4 
                    END,
                    rule_category
            """, (table_name,))
            
            business_rules = cursor.fetchall()
            for rule in business_rules:
                metadata["business_rules"].append({
                    "category": rule[0],
                    "description": rule[1],
                    "sql_example": rule[2],
                    "importance": rule[3]
                })
            
            metadata["has_metadata"] = True
            conn.close()
            
            print(f"✅ 从resource.db成功加载元数据: {len(metadata['column_descriptions'])}个字段, {len(metadata['business_rules'])}个规则")
            return metadata
            
        except Exception as e:
            print(f"⚠️  从resource.db获取元数据失败: {e}")
            return {
                "table_description": None,
                "column_descriptions": [],
                "business_rules": [],
                "has_metadata": False
            }
    
    def execute_business_query(self, sql: str):
        """执行业务数据查询"""
        
        try:
            conn = sqlite3.connect(self.business_db_path)
            cursor = conn.cursor()
            
            cursor.execute(sql)
            results = cursor.fetchall()
            
            # 获取列名
            column_names = [description[0] for description in cursor.description]
            
            conn.close()
            
            print(f"✅ 从fin_data.db成功执行查询，返回 {len(results)} 条记录")
            return column_names, results
            
        except Exception as e:
            print(f"❌ 业务查询失败: {e}")
            return None, None
    
    def construct_enhanced_prompt(self, query: str, metadata: Dict[str, Any]) -> str:
        """构建包含元数据的增强Prompt"""
        
        # 构建元数据增强部分
        metadata_str = ""
        if metadata.get("has_metadata"):
            metadata_str = "\n### 🎯 财务业务元数据 (重要!)\n\n"
            
            # 表描述
            if metadata.get("table_description"):
                table_desc = metadata["table_description"]
                metadata_str += f"**表说明**: {table_desc['description']}\n"
                metadata_str += f"**业务用途**: {table_desc['business_purpose']}\n"
                metadata_str += f"**数据规模**: {table_desc['data_scale']}\n\n"
            
            # 关键业务规则
            if metadata.get("business_rules"):
                metadata_str += "### ⚠️ 关键业务规则 (必须遵守!):\n\n"
                for rule in metadata["business_rules"]:
                    if rule["importance"] in ["CRITICAL", "HIGH"]:
                        metadata_str += f"**【{rule['importance']}】{rule['category']}**:\n"
                        metadata_str += f"- {rule['description']}\n"
                        if rule["sql_example"]:
                            metadata_str += f"- 示例: `{rule['sql_example']}`\n"
                        metadata_str += "\n"
            
            # 关键字段说明
            if metadata.get("column_descriptions"):
                metadata_str += "### 📋 关键字段说明:\n\n"
                
                # 重点显示金额字段
                amount_fields = []
                for col in metadata["column_descriptions"]:
                    col_name = col["column_name"]
                    if "amount" in col_name or col_name == "balance":
                        chinese_name = col["chinese_name"]
                        ai_points = col["ai_understanding_points"]
                        amount_fields.append(f"- **{col_name}** ({chinese_name}): {ai_points}")
                
                if amount_fields:
                    metadata_str += "**💰 金额字段 (关键)**:\n"
                    for field in amount_fields:
                        metadata_str += f"{field}\n"
                    metadata_str += "\n"
        
        # 构建完整prompt
        prompt = f"""
你是一名专业的SQL开发专家，专门将自然语言问题转换为精确的SQL查询。

### 📊 数据库表: financial_data (财务辅助科目余额表)

{metadata_str}

### 🎯 用户查询:
{query}

### 📝 生成要求:

1. **严格遵循业务规则**: 特别注意不同科目类型使用不同的金额字段
2. **正确的字段选择**: 
   - 资产负债类科目 (1xxx, 2xxx, 3xxx) → 使用 `balance` 字段
   - 收入类科目 (60xx) → 使用 `credit_amount` 字段  
   - 成本费用类科目 (64xx, 66xx) → 使用 `debit_amount` 字段
3. **数据类型处理**: balance字段为TEXT类型，需要 `CAST(balance AS REAL)` 转换
4. **科目识别**: 根据科目编号自动识别科目类别并选择正确字段
5. **标准SQL语法**: 生成有效的SELECT语句

请生成准确的SQL查询:
"""
        
        return prompt
    
    def process_query(self, user_query: str):
        """处理完整的查询流程"""
        
        print("🎯 多数据库Text2SQL查询处理")
        print("=" * 60)
        print(f"📝 用户查询: {user_query}")
        
        # 1. 从resource.db获取元数据
        print(f"\n📊 步骤1: 从resource.db获取元数据...")
        metadata = self.get_financial_metadata()
        
        if not metadata.get("has_metadata"):
            print("❌ 无法获取元数据，使用基础模式")
            return None
        
        # 2. 构建增强prompt
        print(f"\n🔧 步骤2: 构建增强Prompt...")
        prompt = self.construct_enhanced_prompt(user_query, metadata)
        print(f"✅ Prompt长度: {len(prompt)} 字符")
        
        # 3. 模拟LLM生成SQL（这里需要实际的LLM调用）
        print(f"\n🤖 步骤3: 生成SQL查询...")
        
        # 根据查询类型生成示例SQL
        if any(word in user_query.lower() for word in ['收入', '营业收入']):
            sql = """
            SELECT 
                accounting_unit_name as '核算单位',
                SUM(credit_amount) as '收入金额'
            FROM financial_data
            WHERE account_code LIKE '60%'
                AND year = 2024 
                AND month = 9
            GROUP BY accounting_unit_name
            ORDER BY SUM(credit_amount) DESC;
            """
        elif any(word in user_query.lower() for word in ['资产', '银行存款']):
            sql = """
            SELECT 
                accounting_unit_name as '核算单位',
                SUM(CAST(balance AS REAL)) as '资产余额'
            FROM financial_data
            WHERE account_code LIKE '1%'
                AND year = 2024 
                AND month = 9
            GROUP BY accounting_unit_name
            ORDER BY SUM(CAST(balance AS REAL)) DESC;
            """
        elif any(word in user_query.lower() for word in ['费用', '成本']):
            sql = """
            SELECT 
                accounting_unit_name as '核算单位',
                SUM(debit_amount) as '费用金额'
            FROM financial_data
            WHERE (account_code LIKE '64%' OR account_code LIKE '66%')
                AND year = 2024 
                AND month = 9
            GROUP BY accounting_unit_name
            ORDER BY SUM(debit_amount) DESC;
            """
        else:
            sql = "SELECT COUNT(*) as '记录数' FROM financial_data;"
        
        print(f"✅ 生成的SQL:")
        print(sql.strip())
        
        # 4. 从fin_data.db执行查询
        print(f"\n💾 步骤4: 从fin_data.db执行查询...")
        columns, results = self.execute_business_query(sql.strip())
        
        if results is not None:
            # 5. 格式化结果
            print(f"\n📊 步骤5: 格式化查询结果...")
            self.format_results(columns, results, limit=5)
            
            return {
                "sql": sql.strip(),
                "columns": columns,
                "results": results,
                "metadata_enhanced": True
            }
        else:
            return None
    
    def format_results(self, column_names, results, limit=5):
        """格式化查询结果"""
        if not results:
            print("没有找到匹配的数据")
            return
        
        print(f"📊 查询结果 (显示前{limit}条):")
        print("-" * 60)
        
        # 打印表头
        header = " | ".join([f"{name:>15}" for name in column_names])
        print(header)
        print("-" * len(header))
        
        # 打印数据行
        for i, row in enumerate(results[:limit]):
            formatted_row = []
            for j, value in enumerate(row):
                if isinstance(value, (int, float)) and j > 0:  # 金额字段格式化
                    formatted_row.append(f"{value:>15,.2f}")
                else:
                    formatted_row.append(f"{str(value):>15}")
            print(" | ".join(formatted_row))
        
        if len(results) > limit:
            print(f"... 还有 {len(results) - limit} 条记录")
        
        # 计算总计
        if len(column_names) > 1 and results:
            try:
                total = sum(row[1] for row in results if isinstance(row[1], (int, float)))
                print("-" * 60)
                print(f"{'总计':>15} | {total:>15,.2f}")
            except:
                pass

def test_multi_database_service():
    """测试多数据库服务"""
    
    service = MultiDatabaseText2SQLService()
    
    # 测试查询
    test_queries = [
        "查询2024年9月的收入情况",
        "显示2024年9月各单位的资产余额",
        "分析2024年9月的费用支出"
    ]
    
    for query in test_queries:
        print("\n" + "=" * 80)
        result = service.process_query(query)
        if result:
            print(f"🎉 查询成功完成")
        else:
            print(f"❌ 查询失败")

if __name__ == "__main__":
    test_multi_database_service()
