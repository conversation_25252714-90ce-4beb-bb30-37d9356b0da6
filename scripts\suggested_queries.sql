-- 建议的SQL查询（替代原查询）

-- 1. 使用贷方金额（当期发生额）
SELECT 
    accounting_unit_name, 
    SUM(credit_amount) AS total_credit_amount
FROM 
    financial_data
WHERE 
    year = 2024 
    AND month = 2 
    AND account_full_name LIKE '主营业务收入%'
GROUP BY 
    accounting_unit_name
ORDER BY 
    total_credit_amount DESC;

-- 2. 使用贷方累计金额
SELECT 
    accounting_unit_name, 
    SUM(credit_cumulative) AS total_credit_cumulative
FROM 
    financial_data
WHERE 
    year = 2024 
    AND month = 2 
    AND account_full_name LIKE '主营业务收入%'
GROUP BY 
    accounting_unit_name
ORDER BY 
    total_credit_cumulative DESC;

-- 3. 查看所有金额字段的汇总（用于对比分析）
SELECT 
    accounting_unit_name,
    SUM(opening_debit_amount) AS total_opening_debit,
    SUM(opening_credit_amount) AS total_opening_credit,
    SUM(debit_amount) AS total_debit,
    SUM(credit_amount) AS total_credit,
    SUM(debit_cumulative) AS total_debit_cumulative,
    SUM(credit_cumulative) AS total_credit_cumulative
FROM 
    financial_data
WHERE 
    year = 2024 
    AND month = 2 
    AND account_full_name LIKE '主营业务收入%'
GROUP BY 
    accounting_unit_name
ORDER BY 
    total_credit_amount DESC;

-- 4. 检查期初余额是否在其他月份有数据
SELECT 
    year, month,
    SUM(opening_credit_amount) AS total_opening_credit,
    SUM(credit_amount) AS total_credit,
    COUNT(*) AS record_count
FROM 
    financial_data
WHERE 
    account_full_name LIKE '主营业务收入%'
GROUP BY 
    year, month
ORDER BY 
    year, month;
