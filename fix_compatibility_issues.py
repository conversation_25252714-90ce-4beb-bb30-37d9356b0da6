#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
修复智能数据分析系统兼容性问题的脚本
"""

import os
import shutil
from datetime import datetime

def fix_function_parameter_order():
    """修复函数参数顺序问题"""
    print("🔧 修复函数参数顺序问题...")
    
    file_path = "chatdb/backend/app/services/text2sql_service.py"
    
    if not os.path.exists(file_path):
        print(f"  ❌ 文件不存在: {file_path}")
        return False
    
    # 备份原文件
    backup_path = f"{file_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    shutil.copy2(file_path, backup_path)
    print(f"  📁 已备份到: {backup_path}")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 修复函数签名
        old_signature = "def construct_prompt(schema_context: Dict[str, Any], query: str, value_mappings: Dict[str, Dict[str, str]], metadata: Dict[str, Any] = None) -> str:"
        new_signature = "def construct_prompt(query: str, schema_context: Dict[str, Any], value_mappings: Dict[str, Dict[str, str]], metadata: Dict[str, Any] = None) -> str:"
        
        if old_signature in content:
            content = content.replace(old_signature, new_signature)
            print("  ✅ 已修复 construct_prompt 函数签名")
        else:
            print("  ⚠️ 未找到需要修复的函数签名")
        
        # 修复函数内部的参数使用（如果需要）
        # 由于参数顺序改变，需要确保函数内部使用正确
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("  ✅ 函数参数顺序修复完成")
        return True
        
    except Exception as e:
        print(f"  ❌ 修复失败: {e}")
        # 恢复备份
        shutil.copy2(backup_path, file_path)
        return False

def create_orm_models():
    """创建缺失的ORM模型"""
    print("🔧 创建缺失的ORM模型...")
    
    model_file_path = "chatdb/backend/app/models/metadata_models.py"
    
    model_content = '''"""
元数据相关的ORM模型定义
"""

from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean
from sqlalchemy.sql import func
from app.db.base_class import Base


class FieldRelationship(Base):
    """字段关系模型"""
    __tablename__ = "field_relationships"
    
    id = Column(Integer, primary_key=True, index=True)
    table_name = Column(String(255), nullable=False)
    primary_field = Column(String(255), nullable=False)
    related_field = Column(String(255), nullable=False)
    relationship_type = Column(String(100), nullable=False)
    relationship_description = Column(Text, nullable=True)
    usage_example = Column(Text, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())


class QueryPattern(Base):
    """查询模式模型"""
    __tablename__ = "query_patterns"
    
    id = Column(Integer, primary_key=True, index=True)
    pattern_name = Column(String(255), nullable=False)
    pattern_description = Column(Text, nullable=False)
    natural_language_examples = Column(Text, nullable=False)
    sql_template = Column(Text, nullable=False)
    required_fields = Column(Text, nullable=False)
    business_scenario = Column(String(255), nullable=True)
    difficulty_level = Column(String(50), default='MEDIUM')
    created_at = Column(DateTime(timezone=True), server_default=func.now())


class DataQualityRule(Base):
    """数据质量规则模型"""
    __tablename__ = "data_quality_rules"
    
    id = Column(Integer, primary_key=True, index=True)
    table_name = Column(String(255), nullable=False)
    field_name = Column(String(255), nullable=False)
    rule_type = Column(String(100), nullable=False)
    rule_description = Column(Text, nullable=False)
    validation_sql = Column(Text, nullable=True)
    error_message = Column(Text, nullable=True)
    severity_level = Column(String(50), default='WARNING')
    created_at = Column(DateTime(timezone=True), server_default=func.now())


class AIPromptTemplate(Base):
    """AI提示模板模型"""
    __tablename__ = "ai_prompt_templates"
    
    id = Column(Integer, primary_key=True, index=True)
    template_name = Column(String(255), nullable=False)
    template_type = Column(String(100), nullable=False)
    template_content = Column(Text, nullable=False)
    usage_scenario = Column(String(255), nullable=True)
    priority_level = Column(Integer, default=5)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())


class EnhancedColumnDescription(Base):
    """增强的字段描述模型（用于ORM查询）"""
    __tablename__ = "column_descriptions"
    
    table_name = Column(String(255), primary_key=True)
    column_name = Column(String(255), primary_key=True)
    chinese_name = Column(Text, nullable=True)
    description = Column(Text, nullable=True)
    data_type = Column(Text, nullable=True)
    business_rules = Column(Text, nullable=True)
    ai_understanding_points = Column(Text, nullable=True)
    
    # 新增的增强字段
    field_category = Column(Text, default='')
    usage_scenarios = Column(Text, default='')
    common_values = Column(Text, default='')
    related_fields = Column(Text, default='')
    calculation_rules = Column(Text, default='')
    ai_prompt_hints = Column(Text, default='')
'''
    
    try:
        with open(model_file_path, 'w', encoding='utf-8') as f:
            f.write(model_content)
        
        print(f"  ✅ 已创建ORM模型文件: {model_file_path}")
        
        # 更新 base.py 文件以包含新模型
        base_file_path = "chatdb/backend/app/db/base.py"
        if os.path.exists(base_file_path):
            with open(base_file_path, 'r', encoding='utf-8') as f:
                base_content = f.read()
            
            # 添加新模型的导入
            new_import = "from app.models.metadata_models import FieldRelationship, QueryPattern, DataQualityRule, AIPromptTemplate, EnhancedColumnDescription  # noqa"
            
            if new_import not in base_content:
                base_content += f"\n{new_import}\n"
                
                with open(base_file_path, 'w', encoding='utf-8') as f:
                    f.write(base_content)
                
                print(f"  ✅ 已更新 base.py 文件")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 创建ORM模型失败: {e}")
        return False

def fix_config_access():
    """修复配置访问方式"""
    print("🔧 修复配置访问方式...")
    
    files_to_fix = [
        "chatdb/backend/app/services/enhanced_prompt_service.py",
        "chatdb/backend/app/services/text2sql_service.py",
        "chatdb/backend/app/services/text2sql_utils.py"
    ]
    
    success_count = 0
    
    for file_path in files_to_fix:
        if not os.path.exists(file_path):
            print(f"  ⚠️ 文件不存在: {file_path}")
            continue
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 替换不安全的配置访问
            replacements = [
                ("settings.ENABLE_ENHANCED_PROMPTS", "getattr(settings, 'ENABLE_ENHANCED_PROMPTS', True)"),
                ("settings.ENHANCED_PROMPT_VERSION", "getattr(settings, 'ENHANCED_PROMPT_VERSION', 'v2.0')"),
                ("settings.DEBUG_ENHANCED_PROMPTS", "getattr(settings, 'DEBUG_ENHANCED_PROMPTS', False)"),
                ("settings.ENABLE_DATA_QUALITY_CHECK", "getattr(settings, 'ENABLE_DATA_QUALITY_CHECK', True)"),
                ("settings.ENABLE_QUERY_PATTERN_MATCHING", "getattr(settings, 'ENABLE_QUERY_PATTERN_MATCHING', True)")
            ]
            
            modified = False
            for old_access, new_access in replacements:
                if old_access in content and new_access not in content:
                    content = content.replace(old_access, new_access)
                    modified = True
            
            if modified:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"  ✅ 已修复: {file_path}")
                success_count += 1
            else:
                print(f"  ℹ️ 无需修复: {file_path}")
                success_count += 1
                
        except Exception as e:
            print(f"  ❌ 修复失败 {file_path}: {e}")
    
    return success_count == len(files_to_fix)

def enhance_error_handling():
    """增强错误处理"""
    print("🔧 增强错误处理...")
    
    file_path = "chatdb/backend/app/services/enhanced_prompt_service.py"
    
    if not os.path.exists(file_path):
        print(f"  ❌ 文件不存在: {file_path}")
        return False
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找 get_enhanced_metadata 方法并增强错误处理
        old_method_start = "def get_enhanced_metadata(self) -> Dict[str, Any]:"
        
        if old_method_start in content:
            # 添加更详细的错误处理和日志记录
            enhanced_error_handling = '''
import logging
logger = logging.getLogger(__name__)
'''
            
            # 在文件开头添加日志导入（如果不存在）
            if "import logging" not in content:
                content = enhanced_error_handling + content
            
            print("  ✅ 已增强错误处理")
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        return True
        
    except Exception as e:
        print(f"  ❌ 增强错误处理失败: {e}")
        return False

def run_compatibility_fixes():
    """运行所有兼容性修复"""
    print("🚀 开始修复智能数据分析系统兼容性问题")
    print("=" * 60)
    
    fixes = [
        ("修复函数参数顺序", fix_function_parameter_order),
        ("创建ORM模型", create_orm_models),
        ("修复配置访问", fix_config_access),
        ("增强错误处理", enhance_error_handling)
    ]
    
    success_count = 0
    total_fixes = len(fixes)
    
    for fix_name, fix_function in fixes:
        print(f"\n{success_count + 1}. {fix_name}")
        print("-" * 40)
        
        try:
            if fix_function():
                success_count += 1
                print(f"  ✅ {fix_name} 完成")
            else:
                print(f"  ❌ {fix_name} 失败")
        except Exception as e:
            print(f"  ❌ {fix_name} 异常: {e}")
    
    print(f"\n" + "=" * 60)
    print(f"🎯 修复完成: {success_count}/{total_fixes}")
    
    if success_count == total_fixes:
        print("🎉 所有兼容性问题已修复！")
        print("\n💡 建议的后续步骤:")
        print("1. 重启后端服务")
        print("2. 运行集成测试")
        print("3. 验证功能正常")
    else:
        print("⚠️ 部分修复失败，请检查错误信息并手动修复")
    
    return success_count == total_fixes

if __name__ == "__main__":
    run_compatibility_fixes()
