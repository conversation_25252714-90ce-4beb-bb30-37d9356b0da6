#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
执行管理费用查询
"""

import sqlite3
import os
import pandas as pd
from datetime import datetime

def execute_management_expense_query():
    """执行管理费用查询并生成结果"""
    
    # 数据库文件路径
    db_path = "fin_data.db"
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return
    
    # 您提供的SQL查询
    sql_query = """
    SELECT 
        accounting_unit_name, 
        SUM(debit_amount) AS total_management_expense
    FROM 
        financial_data
    WHERE 
        year = 2024 
        AND month = 3 
        AND account_full_name LIKE '%管理费用%'
    GROUP BY 
        accounting_unit_name
    ORDER BY 
        total_management_expense DESC;
    """
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("=== 执行管理费用查询 ===\n")
        print("查询SQL:")
        print(sql_query)
        print("\n" + "="*50 + "\n")
        
        # 首先检查数据是否存在
        print("1. 检查数据可用性:")
        
        # 检查2024年3月数据总量
        cursor.execute("SELECT COUNT(*) FROM financial_data WHERE year = 2024 AND month = 3")
        count_2024_03 = cursor.fetchone()[0]
        print(f"   2024年3月总记录数: {count_2024_03:,}")
        
        # 检查包含"管理费用"的记录
        cursor.execute("""
            SELECT COUNT(*) 
            FROM financial_data 
            WHERE year = 2024 AND month = 3 AND account_full_name LIKE '%管理费用%'
        """)
        mgmt_expense_count = cursor.fetchone()[0]
        print(f"   2024年3月管理费用相关记录数: {mgmt_expense_count:,}")
        
        if mgmt_expense_count == 0:
            print("\n❌ 没有找到符合条件的管理费用记录")
            
            # 检查是否有其他月份的管理费用数据
            print("\n2. 检查其他月份的管理费用数据:")
            cursor.execute("""
                SELECT year, month, COUNT(*) 
                FROM financial_data 
                WHERE account_full_name LIKE '%管理费用%'
                GROUP BY year, month
                ORDER BY year, month
            """)
            other_months = cursor.fetchall()
            if other_months:
                print("   其他月份的管理费用数据:")
                for year, month, count in other_months:
                    print(f"     {year}年{month}月: {count:,} 条记录")
            else:
                print("   ❌ 数据库中没有任何管理费用相关记录")
            
            # 检查2024年3月有哪些科目
            print("\n3. 检查2024年3月的科目类型:")
            cursor.execute("""
                SELECT DISTINCT account_full_name 
                FROM financial_data 
                WHERE year = 2024 AND month = 3
                ORDER BY account_full_name
                LIMIT 20
            """)
            accounts = cursor.fetchall()
            if accounts:
                print("   2024年3月的科目示例（前20个）:")
                for account in accounts:
                    print(f"     {account[0]}")
            
            conn.close()
            return
        
        # 执行主查询
        print(f"\n2. 执行查询结果:")
        cursor.execute(sql_query)
        results = cursor.fetchall()
        
        if not results:
            print("   ❌ 查询没有返回任何结果")
        else:
            print(f"   ✅ 查询成功，返回 {len(results)} 条结果\n")
            
            # 显示结果
            print("查询结果:")
            print("-" * 80)
            print(f"{'会计单位名称':<30} {'管理费用总额':<20}")
            print("-" * 80)
            
            total_expense = 0
            for i, (unit_name, expense_amount) in enumerate(results, 1):
                if expense_amount is not None:
                    total_expense += expense_amount
                    print(f"{unit_name:<30} {expense_amount:>15,.2f}")
                else:
                    print(f"{unit_name:<30} {'NULL':>15}")
            
            print("-" * 80)
            print(f"{'总计':<30} {total_expense:>15,.2f}")
            print("-" * 80)
            
            # 保存结果到CSV文件
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            csv_filename = f"management_expense_report_{timestamp}.csv"
            
            # 使用pandas保存结果
            df = pd.DataFrame(results, columns=['会计单位名称', '管理费用总额'])
            df.to_csv(csv_filename, index=False, encoding='utf-8-sig')
            print(f"\n✅ 结果已保存到文件: {csv_filename}")
            
            # 显示统计信息
            print(f"\n3. 统计信息:")
            print(f"   参与统计的会计单位数量: {len(results)}")
            print(f"   管理费用总额: {total_expense:,.2f}")
            if len(results) > 0:
                avg_expense = total_expense / len(results)
                print(f"   平均管理费用: {avg_expense:,.2f}")
                
                # 找出最高和最低的单位
                max_expense = max(results, key=lambda x: x[1] if x[1] is not None else 0)
                min_expense = min(results, key=lambda x: x[1] if x[1] is not None else float('inf'))
                print(f"   最高管理费用单位: {max_expense[0]} ({max_expense[1]:,.2f})")
                print(f"   最低管理费用单位: {min_expense[0]} ({min_expense[1]:,.2f})")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 查询执行过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    execute_management_expense_query()
