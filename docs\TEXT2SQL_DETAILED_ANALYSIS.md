# Text2SQL服务详细分析 - 数据描述关联机制

## 📋 概述

当前的text2sql_service通过多层架构来处理自然语言查询并生成SQL，其中包含了复杂的数据描述关联机制。让我详细分析这个过程。

## 🔍 完整的查询处理流程

### 1. 入口点：process_text2sql_query()

**位置**: `chatdb/backend/app/services/text2sql_service.py:116`

```python
def process_text2sql_query(db: Session, connection: DBConnection, natural_language_query: str) -> QueryResponse:
```

**核心步骤**:
1. 检索相关表结构 (`retrieve_relevant_schema`)
2. 获取值映射 (`get_value_mappings`)
3. 构建提示 (`construct_prompt`)
4. 调用LLM API (`call_llm_api`)
5. 提取和验证SQL
6. 执行查询

## 🗃️ 数据描述关联的核心机制

### 2. Schema检索：retrieve_relevant_schema()

**位置**: `chatdb/backend/app/services/text2sql_utils.py:396`

这是数据描述关联的**核心函数**，它通过以下步骤关联查询与数据描述：

#### 步骤2.1: LLM查询分析
```python
query_analysis = await analyze_query_with_llm(query)
```

**功能**: 使用LLM分析自然语言查询，提取：
- **entities**: 查询中的实体名称
- **relationships**: 实体间关系
- **query_intent**: 查询意图
- **likely_aggregations**: 可能的聚合操作
- **time_related**: 是否涉及时间
- **comparison_related**: 是否涉及比较

**示例分析结果**:
```json
{
    "entities": ["收入", "2024年", "9月"],
    "relationships": ["时间范围", "金额统计"],
    "query_intent": "查询特定时间段的收入数据",
    "likely_aggregations": ["sum"],
    "time_related": true,
    "comparison_related": false
}
```

#### 步骤2.2: Neo4j图数据库查询

**连接Neo4j**:
```python
driver = GraphDatabase.driver(
    settings.NEO4J_URI,
    auth=(settings.NEO4J_USER, settings.NEO4J_PASSWORD)
)
```

**获取所有表信息**:
```cypher
MATCH (t:Table {connection_id: $connection_id})
RETURN t.id AS id, t.name AS name, t.description AS description
```

**语义搜索相关表**:
```python
relevant_table_ids = await find_relevant_tables_semantic(query, query_analysis, all_tables)
```

#### 步骤2.3: 基于实体的列匹配

**关键代码**:
```cypher
MATCH (c:Column {connection_id: $connection_id})
WHERE toLower(c.name) CONTAINS $entity OR toLower(c.description) CONTAINS $entity
MATCH (t:Table)-[:HAS_COLUMN]->(c)
RETURN c.id, c.name, c.type, c.description, c.is_pk, c.is_fk, t.id, t.name
```

**工作原理**:
- 遍历查询分析中的每个实体
- 在Neo4j中搜索列名或列描述包含该实体的列
- 通过列找到相关的表
- 建立实体→列→表的关联关系

#### 步骤2.4: 外键关系扩展

**扩展相关表**:
```cypher
MATCH (t1:Table {connection_id: $connection_id})-[:HAS_COLUMN]->
      (c1:Column)-[:REFERENCES]->
      (c2:Column)<-[:HAS_COLUMN]-(t2:Table {connection_id: $connection_id})
WHERE t1.id IN $table_ids AND NOT t2.id IN $table_ids
RETURN t2.id, t2.name, t2.description, ...
```

**功能**: 通过外键关系找到与已识别表相关的其他表

#### 步骤2.5: LLM二次过滤

**过滤扩展表**:
```python
filtered_expanded_tables = await filter_expanded_tables_with_llm(
    query, query_analysis, expanded_tables, table_relevance_scores
)
```

**功能**: 使用LLM评估扩展的表是否真正与查询相关

### 3. 数据库元数据获取

#### 步骤3.1: 从SQLAlchemy ORM获取详细信息

**获取表信息**:
```python
all_tables_from_db = crud.schema_table.get_by_connection(db=db, connection_id=connection_id)
```

**获取列信息**:
```python
table_columns = crud.schema_column.get_by_table(db=db, table_id=table["id"])
```

**构建列描述**:
```python
columns_list.append({
    "id": column.id,
    "name": column.column_name,
    "type": column.data_type,
    "description": column.description,  # ← 关键：列描述
    "is_primary_key": column.is_primary_key,
    "is_foreign_key": column.is_foreign_key,
    "table_id": table["id"],
    "table_name": table["name"]
})
```

#### 步骤3.2: 获取表关系

**获取表间关系**:
```python
all_relationships = crud.schema_relationship.get_by_connection(db=db, connection_id=connection_id)
```

### 4. 值映射获取：get_value_mappings()

**功能**: 获取自然语言术语与数据库值的映射关系

**示例**:
```python
value_mappings = {
    "company_name": {
        "中石化": "中国石化",
        "工行": "中国工商银行"
    }
}
```

## 🔧 Prompt构建机制

### 5. construct_prompt()

**位置**: `chatdb/backend/app/services/text2sql_service.py:19`

#### 步骤5.1: 格式化Schema信息

**调用**:
```python
schema_str = format_schema_for_prompt(schema_context)
```

**生成的Schema格式**:
```sql
-- 表: financial_data
-- 描述: 财务辅助科目余额表
CREATE TABLE financial_data (
    year INTEGER, -- 年度
    month INTEGER, -- 月份
    account_code INTEGER, -- 科目编号
    account_name TEXT, -- 科目名称
    credit_amount REAL, -- 贷方金额
    debit_amount REAL, -- 借方金额
    balance TEXT -- 余额
);
```

#### 步骤5.2: 添加值映射

**值映射格式**:
```sql
-- 值映射:
-- 对于 company_name:
--   自然语言中的'中石化'指数据库中的'中国石化'
--   自然语言中的'工行'指数据库中的'中国工商银行'
```

#### 步骤5.3: 构建完整Prompt

**Prompt结构**:
```
你是一名专业的SQL开发专家...

### 数据库结构:
```sql
[Schema信息 + 值映射]
```

### 自然语言问题:
"[用户查询]"

### 指令:
[详细的SQL生成指令]
```

## 🤖 LLM增强机制

### 6. call_llm_api()

**位置**: `chatdb/backend/app/services/text2sql_service.py:65`

#### 系统消息中的财务专业知识

**关键部分**:
```python
system_message = """
## 财务数据库专业知识（重要）：
对于main.financial_data表的查询，必须严格遵循以下规则：

### 科目分类与字段使用规则：
1. **资产负债类科目查询** (银行存款,应付账款,应收账款等)：
   - 必须使用 balance 字段进行金额汇总
   - 示例：SELECT SUM(CAST(balance AS REAL)) FROM financial_data WHERE account_code LIKE '1%'

2. **收入类科目查询** (主营业务收入,其他业务收入)：
   - 必须使用 credit_amount 字段（当期）
   - 示例：SELECT SUM(credit_amount) FROM financial_data WHERE account_code LIKE '60%'

3. **成本费用类科目查询** (主营业务成本,管理费用,财务费用,销售费用等)：
   - 必须使用 debit_amount 字段（当期）
   - 示例：SELECT SUM(debit_amount) FROM financial_data WHERE account_code LIKE '66%'
"""
```

## 🔗 数据描述关联的完整链路

### 查询处理的数据流

```
用户查询: "查询2024年9月的收入情况"
    ↓
1. LLM分析查询
    → entities: ["收入", "2024年", "9月"]
    → query_intent: "查询特定时间段的收入数据"
    ↓
2. Neo4j语义搜索
    → 搜索包含"收入"的列: credit_amount, account_name
    → 找到相关表: financial_data
    ↓
3. SQLAlchemy ORM获取详细信息
    → 表描述: "财务辅助科目余额表"
    → 列描述: credit_amount - "贷方金额"
    ↓
4. 构建增强Prompt
    → Schema: CREATE TABLE financial_data (credit_amount REAL -- 贷方金额, ...)
    → 业务规则: "收入类科目必须使用credit_amount字段"
    ↓
5. LLM生成SQL
    → SELECT SUM(credit_amount) FROM financial_data WHERE account_code LIKE '60%'
```

## 🎯 当前系统的优势与局限

### ✅ 优势

1. **多层语义理解**: LLM分析 + Neo4j图搜索 + ORM元数据
2. **智能表发现**: 基于实体和关系的表关联
3. **业务规则集成**: 系统消息中包含财务专业知识
4. **值映射支持**: 处理自然语言与数据库值的差异

### ⚠️ 局限性

1. **元数据来源单一**: 仅依赖ORM的基础元数据（表名、列名、数据类型）
2. **业务规则硬编码**: 财务规则写在系统消息中，不够灵活
3. **缺乏字段语义**: 没有中文名称和AI理解要点
4. **规则更新困难**: 业务规则变更需要修改代码

## 🚀 我们的元数据系统如何增强这个流程

### 集成点1: 在retrieve_relevant_schema()中

**当前**: 仅获取基础ORM元数据
**增强**: 同时从resource.db获取我们的元数据

### 集成点2: 在construct_prompt()中

**当前**: 基础Schema + 硬编码规则
**增强**: Schema + 动态元数据 + 业务规则表

### 集成点3: 在call_llm_api()中

**当前**: 静态系统消息
**增强**: 动态系统消息 + 元数据驱动的规则

## 📊 总结

当前的text2sql_service已经有了相当复杂的数据描述关联机制，但主要依赖：

1. **Neo4j图数据库**: 存储表和列的关系
2. **SQLAlchemy ORM**: 提供基础元数据
3. **硬编码业务规则**: 在系统消息中定义

我们的元数据系统将在这个基础上提供：
- **丰富的字段语义**: 中文名称、AI理解要点
- **动态业务规则**: 可配置的规则表
- **专业领域知识**: 财务专业的元数据

这将使AI模型能够更准确地理解财务数据的业务含义！
