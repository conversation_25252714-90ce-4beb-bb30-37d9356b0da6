#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os

def view_financial_data_descriptions():
    """查看financial_data表的描述信息"""
    
    db_path = 'fin_data.db'
    if not os.path.exists(db_path):
        print(f'数据库文件不存在: {db_path}')
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("=" * 80)
        print("FINANCIAL_DATA 表描述信息")
        print("=" * 80)
        
        # 1. 显示表级别描述
        cursor.execute('SELECT * FROM table_descriptions WHERE table_name = "financial_data"')
        table_info = cursor.fetchone()
        if table_info:
            print(f"\n📊 表名: {table_info[0]}")
            print(f"📝 描述: {table_info[1]}")
            print(f"🎯 业务用途: {table_info[2]}")
            print(f"📈 数据规模: {table_info[3]}")
            print(f"⏰ 创建时间: {table_info[4]}")
        
        # 2. 显示字段描述（按类别分组）
        print("\n" + "=" * 80)
        print("字段描述信息")
        print("=" * 80)
        
        # 时间维度字段
        print("\n🕐 时间维度字段:")
        cursor.execute('''
            SELECT column_name, chinese_name, description, data_type, ai_understanding_points
            FROM column_descriptions 
            WHERE table_name = "financial_data" AND column_name IN ("year", "month")
            ORDER BY column_name
        ''')
        for row in cursor.fetchall():
            print(f"  • {row[0]} ({row[1]}) - {row[2]} [{row[3]}]")
            print(f"    AI理解要点: {row[4]}")
        
        # 组织架构字段
        print("\n🏢 组织架构字段:")
        cursor.execute('''
            SELECT column_name, chinese_name, description, data_type, ai_understanding_points
            FROM column_descriptions 
            WHERE table_name = "financial_data" AND column_name IN ("accounting_organization", "accounting_unit_name")
            ORDER BY column_name
        ''')
        for row in cursor.fetchall():
            print(f"  • {row[0]} ({row[1]}) - {row[2]} [{row[3]}]")
            print(f"    AI理解要点: {row[4]}")
        
        # 会计科目字段
        print("\n📋 会计科目字段:")
        cursor.execute('''
            SELECT column_name, chinese_name, description, data_type, ai_understanding_points
            FROM column_descriptions 
            WHERE table_name = "financial_data" AND column_name LIKE "account_%"
            ORDER BY column_name
        ''')
        for row in cursor.fetchall():
            print(f"  • {row[0]} ({row[1]}) - {row[2]} [{row[3]}]")
            print(f"    AI理解要点: {row[4]}")
        
        # 金额字段
        print("\n💰 金额字段（核心财务数据）:")
        cursor.execute('''
            SELECT column_name, chinese_name, description, data_type, ai_understanding_points
            FROM column_descriptions 
            WHERE table_name = "financial_data" AND (
                column_name LIKE "%amount%" OR 
                column_name LIKE "%cumulative%" OR 
                column_name = "balance"
            )
            ORDER BY column_name
        ''')
        for row in cursor.fetchall():
            print(f"  • {row[0]} ({row[1]}) - {row[2]} [{row[3]}]")
            print(f"    AI理解要点: {row[4]}")
        
        # 项目管理字段
        print("\n📁 项目管理字段:")
        cursor.execute('''
            SELECT column_name, chinese_name, description, data_type, ai_understanding_points
            FROM column_descriptions 
            WHERE table_name = "financial_data" AND column_name LIKE "project_%"
            ORDER BY column_name
        ''')
        for row in cursor.fetchall():
            print(f"  • {row[0]} ({row[1]}) - {row[2]} [{row[3]}]")
            print(f"    AI理解要点: {row[4]}")
        
        # 银行信息字段
        print("\n🏦 银行信息字段:")
        cursor.execute('''
            SELECT column_name, chinese_name, description, data_type, ai_understanding_points
            FROM column_descriptions 
            WHERE table_name = "financial_data" AND (
                column_name LIKE "bank_%" OR 
                column_name LIKE "financial_institution_%"
            )
            ORDER BY column_name
        ''')
        for row in cursor.fetchall():
            print(f"  • {row[0]} ({row[1]}) - {row[2]} [{row[3]}]")
            print(f"    AI理解要点: {row[4]}")
        
        # 3. 显示业务规则
        print("\n" + "=" * 80)
        print("关键业务规则")
        print("=" * 80)
        
        cursor.execute('''
            SELECT rule_category, rule_description, sql_example, importance_level
            FROM business_rules 
            WHERE table_name = "financial_data"
            ORDER BY importance_level DESC, rule_category
        ''')
        
        for i, row in enumerate(cursor.fetchall(), 1):
            print(f"\n{i}. 【{row[3]}】{row[0]}")
            print(f"   规则: {row[1]}")
            if row[2]:
                print(f"   示例: {row[2]}")
        
        # 4. 显示统计信息
        print("\n" + "=" * 80)
        print("统计信息")
        print("=" * 80)
        
        cursor.execute('SELECT COUNT(*) FROM column_descriptions WHERE table_name = "financial_data"')
        col_count = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM business_rules WHERE table_name = "financial_data"')
        rule_count = cursor.fetchone()[0]
        
        cursor.execute('SELECT COUNT(*) FROM financial_data')
        data_count = cursor.fetchone()[0]
        
        print(f"📊 字段描述数量: {col_count}")
        print(f"📋 业务规则数量: {rule_count}")
        print(f"📈 数据记录数量: {data_count:,}")
        
        conn.close()
        
    except Exception as e:
        print(f'查看描述时出错: {e}')

def create_metadata_view():
    """创建一个包含元数据的视图"""
    
    db_path = 'fin_data.db'
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 创建一个视图，包含字段的中文名和描述
        print("创建元数据视图...")
        cursor.execute('''
            CREATE VIEW IF NOT EXISTS financial_data_with_metadata AS
            SELECT 
                'financial_data' as table_name,
                td.description as table_description,
                td.business_purpose,
                td.data_scale,
                (SELECT COUNT(*) FROM financial_data) as current_record_count
            FROM table_descriptions td
            WHERE td.table_name = 'financial_data'
        ''')
        
        # 创建字段元数据视图
        cursor.execute('''
            CREATE VIEW IF NOT EXISTS financial_data_columns_metadata AS
            SELECT 
                column_name,
                chinese_name,
                description,
                data_type,
                business_rules,
                ai_understanding_points
            FROM column_descriptions
            WHERE table_name = 'financial_data'
            ORDER BY 
                CASE 
                    WHEN column_name IN ('year', 'month') THEN 1
                    WHEN column_name LIKE 'accounting_%' THEN 2
                    WHEN column_name LIKE 'account_%' THEN 3
                    WHEN column_name LIKE '%amount%' OR column_name LIKE '%cumulative%' OR column_name = 'balance' THEN 4
                    WHEN column_name LIKE 'project_%' THEN 5
                    WHEN column_name LIKE 'bank_%' OR column_name LIKE 'financial_institution_%' THEN 6
                    ELSE 7
                END,
                column_name
        ''')
        
        conn.commit()
        print("✅ 元数据视图创建成功！")
        
        # 测试视图
        print("\n测试表元数据视图:")
        cursor.execute('SELECT * FROM financial_data_with_metadata')
        result = cursor.fetchone()
        if result:
            print(f"表名: {result[0]}")
            print(f"描述: {result[1]}")
            print(f"当前记录数: {result[4]:,}")
        
        conn.close()
        
    except Exception as e:
        print(f'创建视图时出错: {e}')

if __name__ == '__main__':
    view_financial_data_descriptions()
    print("\n" + "=" * 80)
    create_metadata_view()
