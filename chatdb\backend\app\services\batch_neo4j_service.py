"""
批量Neo4j查询服务
优化多个查询的执行效率
"""
import asyncio
import logging
from typing import Dict, Any, List, Tuple
from dataclasses import dataclass

from app.services.neo4j_connection_pool import get_neo4j_pool

logger = logging.getLogger(__name__)


@dataclass
class BatchQuery:
    """批量查询项"""
    query_id: str
    cypher: str
    parameters: Dict[str, Any]
    callback: callable = None


class BatchNeo4jService:
    """批量Neo4j查询服务"""
    
    def __init__(self):
        self.batch_size = 10
        self.batch_timeout = 0.1  # 100ms批量超时
        self.pending_queries = []
        self.batch_lock = asyncio.Lock()
    
    async def execute_batch_queries(self, queries: List[BatchQuery]) -> Dict[str, Any]:
        """执行批量查询"""
        neo4j_pool = await get_neo4j_pool()
        results = {}
        
        try:
            async with neo4j_pool.get_session() as session:
                # 并行执行多个查询
                tasks = []
                for query in queries:
                    task = asyncio.create_task(
                        self._execute_single_query(session, query)
                    )
                    tasks.append((query.query_id, task))
                
                # 等待所有查询完成
                for query_id, task in tasks:
                    try:
                        result = await task
                        results[query_id] = result
                    except Exception as e:
                        logger.error(f"批量查询 {query_id} 失败: {str(e)}")
                        results[query_id] = {'error': str(e)}
        
        except Exception as e:
            logger.error(f"批量查询执行失败: {str(e)}")
            raise
        
        return results
    
    async def _execute_single_query(self, session, query: BatchQuery) -> List[Dict]:
        """执行单个查询"""
        result = await session.run(query.cypher, query.parameters)
        return await result.data()
    
    async def get_schema_data_batch(self, connection_id: int) -> Dict[str, Any]:
        """批量获取表结构数据"""
        queries = [
            BatchQuery(
                query_id="tables",
                cypher="""
                MATCH (t:Table {connection_id: $connection_id})
                RETURN t.id AS id, t.name AS name, t.description AS description
                """,
                parameters={'connection_id': connection_id}
            ),
            BatchQuery(
                query_id="columns",
                cypher="""
                MATCH (t:Table {connection_id: $connection_id})-[:HAS_COLUMN]->(c:Column)
                RETURN c.id AS id, c.name AS name, c.type AS type, 
                       c.description AS description, c.is_pk AS is_pk, 
                       c.is_fk AS is_fk, t.id AS table_id, t.name AS table_name
                """,
                parameters={'connection_id': connection_id}
            ),
            BatchQuery(
                query_id="relationships",
                cypher="""
                MATCH (t1:Table {connection_id: $connection_id})-[:HAS_COLUMN]->(c1:Column)
                      -[:REFERENCES]->(c2:Column)<-[:HAS_COLUMN]-(t2:Table {connection_id: $connection_id})
                RETURN t1.name AS source_table, c1.name AS source_column,
                       t2.name AS target_table, c2.name AS target_column,
                       'foreign_key' AS relationship_type
                """,
                parameters={'connection_id': connection_id}
            )
        ]
        
        results = await self.execute_batch_queries(queries)
        
        return {
            'tables': results.get('tables', []),
            'columns': results.get('columns', []),
            'relationships': results.get('relationships', [])
        }
    
    async def get_qa_pairs_with_context(self, connection_id: int, table_names: List[str], 
                                      query_type: str = None, top_k: int = 20) -> Dict[str, Any]:
        """批量获取问答对及相关上下文"""
        queries = [
            BatchQuery(
                query_id="structural_qa",
                cypher="""
                MATCH (qa:QAPair)-[:USES_TABLES]->(t:Table)
                WHERE t.name IN $table_names AND qa.connection_id = $connection_id
                WITH qa, count(t) as table_overlap, collect(t.name) as used_tables
                ORDER BY table_overlap DESC, qa.success_rate DESC
                LIMIT $top_k
                RETURN qa, table_overlap, used_tables
                """,
                parameters={
                    'table_names': table_names,
                    'connection_id': connection_id,
                    'top_k': top_k
                }
            )
        ]
        
        # 如果指定了查询类型，添加模式查询
        if query_type:
            queries.append(
                BatchQuery(
                    query_id="pattern_qa",
                    cypher="""
                    MATCH (qa:QAPair)-[:FOLLOWS_PATTERN]->(p:QueryPattern)
                    WHERE p.name = $query_type AND qa.connection_id = $connection_id
                    RETURN qa, p.usage_count
                    ORDER BY qa.success_rate DESC, p.usage_count DESC
                    LIMIT $top_k
                    """,
                    parameters={
                        'query_type': query_type,
                        'connection_id': connection_id,
                        'top_k': top_k
                    }
                )
            )
        
        results = await self.execute_batch_queries(queries)
        
        return {
            'structural_results': results.get('structural_qa', []),
            'pattern_results': results.get('pattern_qa', []) if query_type else []
        }
    
    async def optimize_connection_queries(self, connection_id: int) -> Dict[str, Any]:
        """优化连接相关的所有查询"""
        # 创建一个大的批量查询，一次性获取所有需要的数据
        optimized_query = """
        // 获取表信息
        MATCH (t:Table {connection_id: $connection_id})
        OPTIONAL MATCH (t)-[:HAS_COLUMN]->(c:Column)
        OPTIONAL MATCH (c)-[:REFERENCES]->(ref_c:Column)<-[:HAS_COLUMN]-(ref_t:Table)
        
        // 获取问答对信息
        OPTIONAL MATCH (qa:QAPair {connection_id: $connection_id})-[:USES_TABLES]->(t)
        
        RETURN {
            tables: collect(DISTINCT {
                id: t.id, 
                name: t.name, 
                description: t.description
            }),
            columns: collect(DISTINCT {
                id: c.id,
                name: c.name,
                type: c.type,
                description: c.description,
                is_pk: c.is_pk,
                is_fk: c.is_fk,
                table_id: t.id,
                table_name: t.name
            }),
            relationships: collect(DISTINCT {
                source_table: t.name,
                source_column: c.name,
                target_table: ref_t.name,
                target_column: ref_c.name,
                relationship_type: 'foreign_key'
            }),
            qa_pairs: collect(DISTINCT {
                id: qa.id,
                question: qa.question,
                sql: qa.sql,
                query_type: qa.query_type,
                success_rate: qa.success_rate,
                difficulty_level: qa.difficulty_level
            })
        } AS result
        """
        
        neo4j_pool = await get_neo4j_pool()
        result = await neo4j_pool.execute_read_query(
            optimized_query,
            {'connection_id': connection_id}
        )
        
        return result[0]['result'] if result else {}


# 全局批量查询服务实例
batch_neo4j_service = BatchNeo4jService()
