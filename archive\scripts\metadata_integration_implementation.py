#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
元数据系统与大模型集成实施方案
解决当前大模型无法使用我们构建的元数据系统的问题
"""

import sqlite3
import os
import shutil
from datetime import datetime

def analyze_current_databases():
    """分析当前数据库状态"""
    print("🔍 分析当前数据库状态...")
    
    resource_db = "chatdb/backend/resource.db"
    fin_data_db = "fin_data.db"
    
    print(f"\n📊 数据库文件检查:")
    
    # 检查resource.db
    if os.path.exists(resource_db):
        print(f"✅ {resource_db} 存在")
        conn = sqlite3.connect(resource_db)
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        print(f"   表: {tables}")
        
        # 检查是否有financial_data表
        if 'financial_data' in tables:
            cursor.execute("SELECT COUNT(*) FROM financial_data")
            count = cursor.fetchone()[0]
            print(f"   financial_data记录数: {count:,}")
        else:
            print("   ❌ 缺少financial_data表")
        
        # 检查是否有元数据表
        metadata_tables = ['table_descriptions', 'column_descriptions', 'business_rules']
        for table in metadata_tables:
            if table in tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"   ✅ {table}: {count}条记录")
            else:
                print(f"   ❌ 缺少{table}表")
        
        conn.close()
    else:
        print(f"❌ {resource_db} 不存在")
    
    # 检查fin_data.db
    if os.path.exists(fin_data_db):
        print(f"\n✅ {fin_data_db} 存在")
        conn = sqlite3.connect(fin_data_db)
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [row[0] for row in cursor.fetchall()]
        print(f"   表: {tables}")
        
        # 检查financial_data表
        if 'financial_data' in tables:
            cursor.execute("SELECT COUNT(*) FROM financial_data")
            count = cursor.fetchone()[0]
            print(f"   ✅ financial_data记录数: {count:,}")
        
        # 检查元数据表
        metadata_tables = ['table_descriptions', 'column_descriptions', 'business_rules']
        for table in metadata_tables:
            if table in tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"   ✅ {table}: {count}条记录")
        
        conn.close()
    else:
        print(f"❌ {fin_data_db} 不存在")

def solution_1_migrate_to_resource_db():
    """方案1: 将fin_data.db中的表迁移到resource.db"""
    print("\n🚀 执行方案1: 数据迁移到resource.db")
    
    resource_db = "chatdb/backend/resource.db"
    fin_data_db = "fin_data.db"
    
    if not os.path.exists(fin_data_db):
        print(f"❌ 源数据库 {fin_data_db} 不存在")
        return False
    
    if not os.path.exists(resource_db):
        print(f"❌ 目标数据库 {resource_db} 不存在")
        return False
    
    try:
        # 备份resource.db
        backup_path = f"{resource_db}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(resource_db, backup_path)
        print(f"✅ 已备份resource.db到: {backup_path}")
        
        # 连接两个数据库
        source_conn = sqlite3.connect(fin_data_db)
        target_conn = sqlite3.connect(resource_db)
        
        # 获取需要迁移的表
        tables_to_migrate = ['financial_data', 'table_descriptions', 'column_descriptions', 'business_rules']
        
        for table_name in tables_to_migrate:
            print(f"📦 迁移表: {table_name}")
            
            # 检查源表是否存在
            source_cursor = source_conn.cursor()
            source_cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}'")
            if not source_cursor.fetchone():
                print(f"   ⚠️  源表 {table_name} 不存在，跳过")
                continue
            
            # 获取表结构
            source_cursor.execute(f"SELECT sql FROM sqlite_master WHERE type='table' AND name='{table_name}'")
            create_sql = source_cursor.fetchone()[0]
            
            # 在目标数据库中创建表
            target_cursor = target_conn.cursor()
            target_cursor.execute(f"DROP TABLE IF EXISTS {table_name}")
            target_cursor.execute(create_sql)
            
            # 迁移数据
            source_cursor.execute(f"SELECT * FROM {table_name}")
            rows = source_cursor.fetchall()
            
            if rows:
                # 获取列数
                column_count = len(rows[0])
                placeholders = ','.join(['?' for _ in range(column_count)])
                target_cursor.executemany(f"INSERT INTO {table_name} VALUES ({placeholders})", rows)
                print(f"   ✅ 迁移了 {len(rows):,} 条记录")
            else:
                print(f"   ⚠️  表 {table_name} 为空")
        
        # 迁移视图
        views_to_migrate = ['financial_data_with_metadata', 'financial_data_columns_metadata']
        for view_name in views_to_migrate:
            source_cursor.execute(f"SELECT sql FROM sqlite_master WHERE type='view' AND name='{view_name}'")
            view_sql = source_cursor.fetchone()
            if view_sql:
                target_cursor.execute(f"DROP VIEW IF EXISTS {view_name}")
                target_cursor.execute(view_sql[0])
                print(f"   ✅ 迁移视图: {view_name}")
        
        target_conn.commit()
        source_conn.close()
        target_conn.close()
        
        print("🎉 数据迁移完成！")
        return True
        
    except Exception as e:
        print(f"❌ 迁移失败: {e}")
        return False

def solution_2_update_config():
    """方案2: 修改配置文件指向fin_data.db"""
    print("\n🚀 执行方案2: 修改配置文件")
    
    env_file = "chatdb/backend/.env"
    fin_data_db = os.path.abspath("fin_data.db")
    
    if not os.path.exists(env_file):
        print(f"❌ 配置文件 {env_file} 不存在")
        return False
    
    try:
        # 备份原配置文件
        backup_path = f"{env_file}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(env_file, backup_path)
        print(f"✅ 已备份配置文件到: {backup_path}")
        
        # 读取配置文件
        with open(env_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # 修改SQLITE_DB_PATH
        modified = False
        for i, line in enumerate(lines):
            if line.startswith('SQLITE_DB_PATH='):
                old_path = line.strip()
                lines[i] = f'SQLITE_DB_PATH={fin_data_db}\n'
                print(f"📝 修改配置:")
                print(f"   旧值: {old_path}")
                print(f"   新值: SQLITE_DB_PATH={fin_data_db}")
                modified = True
                break
        
        if not modified:
            print("❌ 未找到SQLITE_DB_PATH配置项")
            return False
        
        # 写回配置文件
        with open(env_file, 'w', encoding='utf-8') as f:
            f.writelines(lines)
        
        print("✅ 配置文件修改完成")
        return True
        
    except Exception as e:
        print(f"❌ 配置修改失败: {e}")
        return False

def verify_integration():
    """验证集成结果"""
    print("\n🧪 验证集成结果...")
    
    # 根据当前配置确定数据库路径
    env_file = "chatdb/backend/.env"
    db_path = "chatdb/backend/resource.db"  # 默认值
    
    if os.path.exists(env_file):
        with open(env_file, 'r', encoding='utf-8') as f:
            for line in f:
                if line.startswith('SQLITE_DB_PATH='):
                    db_path = line.split('=', 1)[1].strip()
                    break
    
    print(f"📍 检查数据库: {db_path}")
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查必要的表
        required_tables = ['financial_data', 'table_descriptions', 'column_descriptions', 'business_rules']
        missing_tables = []
        
        for table in required_tables:
            cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'")
            if not cursor.fetchone():
                missing_tables.append(table)
        
        if missing_tables:
            print(f"❌ 缺少必要的表: {missing_tables}")
            return False
        
        # 检查数据
        cursor.execute("SELECT COUNT(*) FROM financial_data")
        fd_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM table_descriptions WHERE table_name='financial_data'")
        td_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM column_descriptions WHERE table_name='financial_data'")
        cd_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM business_rules WHERE table_name='financial_data'")
        br_count = cursor.fetchone()[0]
        
        print(f"✅ 数据验证:")
        print(f"   financial_data: {fd_count:,} 条记录")
        print(f"   table_descriptions: {td_count} 条记录")
        print(f"   column_descriptions: {cd_count} 条记录")
        print(f"   business_rules: {br_count} 条记录")
        
        # 测试元数据查询
        cursor.execute("""
            SELECT rule_description FROM business_rules 
            WHERE table_name='financial_data' AND importance_level='CRITICAL'
            LIMIT 1
        """)
        rule = cursor.fetchone()
        if rule:
            print(f"✅ 元数据查询测试成功: {rule[0][:50]}...")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 元数据系统与大模型集成实施方案")
    print("=" * 60)
    
    # 分析当前状态
    analyze_current_databases()
    
    print("\n" + "=" * 60)
    print("🛠️  可选方案:")
    print("1. 迁移数据到resource.db (推荐)")
    print("2. 修改配置指向fin_data.db")
    print("3. 仅验证当前状态")
    
    choice = input("\n请选择方案 (1/2/3): ").strip()
    
    success = False
    if choice == "1":
        success = solution_1_migrate_to_resource_db()
    elif choice == "2":
        success = solution_2_update_config()
    elif choice == "3":
        success = verify_integration()
    else:
        print("❌ 无效选择")
        return
    
    if success:
        print("\n" + "=" * 60)
        verify_integration()
        
        print("\n🎉 集成完成！")
        print("\n📋 下一步:")
        print("1. 重启chatdb后端服务")
        print("2. 修改text2sql_service.py集成元数据查询")
        print("3. 测试财务数据查询功能")
    else:
        print("\n❌ 集成失败，请检查错误信息")

if __name__ == '__main__':
    main()
