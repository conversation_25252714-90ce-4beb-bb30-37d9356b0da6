# 元数据专项迁移方案 - 架构友好的解决方案

## 🎯 方案概述

**推荐方案**: 仅将元数据表迁移到resource.db，保持业务数据在fin_data.db

这是最符合架构设计原则的方案！

## 🏗️ 架构设计理念

### 数据分层原则

```
📊 元数据层 (resource.db)
├── 系统配置表 (dbconnection, schematable等)
├── 元数据表 (table_descriptions, column_descriptions, business_rules)
└── 应用管理表 (chat_history, value_mappings等)

💾 业务数据层 (fin_data.db)  
├── 核心业务表 (financial_data)
├── 其他业务表 (future business tables)
└── 业务相关视图
```

### 优势分析

1. **架构清晰**: 元数据与业务数据分离
2. **性能优化**: 元数据查询不影响业务数据性能
3. **维护简单**: 各层职责明确
4. **扩展性好**: 未来可以轻松添加其他业务数据库

## 🔧 具体实施方案

### 迁移内容

**仅迁移元数据表**:
- ✅ `table_descriptions` (1条记录)
- ✅ `column_descriptions` (31条记录)  
- ✅ `business_rules` (5条记录)
- ✅ 相关视图 (financial_data_with_metadata等)

**保留在fin_data.db**:
- ✅ `financial_data` (723,333条记录) - 业务数据
- ✅ 其他未来的业务表

### 系统配置调整

**多数据库连接配置**:
```python
# 在 app/core/config.py 中添加
class Settings(BaseSettings):
    # 元数据库配置
    METADATA_DB_PATH: str = os.getenv("METADATA_DB_PATH", "resource.db")
    
    # 业务数据库配置  
    BUSINESS_DB_PATH: str = os.getenv("BUSINESS_DB_PATH", "fin_data.db")
```

**数据库连接管理**:
```python
# 在 app/db/session.py 中添加
def get_metadata_db_url():
    """元数据库连接"""
    return f"sqlite:///{settings.METADATA_DB_PATH}"

def get_business_db_url():
    """业务数据库连接"""
    return f"sqlite:///{settings.BUSINESS_DB_PATH}"

# 创建两个引擎
metadata_engine = create_engine(get_metadata_db_url())
business_engine = create_engine(get_business_db_url())
```

## 🚀 迁移实施步骤

### 步骤1: 备份数据
```bash
cp resource.db resource.db.backup_$(date +%Y%m%d_%H%M%S)
cp fin_data.db fin_data.db.backup_$(date +%Y%m%d_%H%M%S)
```

### 步骤2: 迁移元数据表
```python
def migrate_metadata_only():
    """仅迁移元数据表到resource.db"""
    
    source_db = "fin_data.db"
    target_db = "resource.db"
    
    # 要迁移的元数据表
    metadata_tables = [
        'table_descriptions',
        'column_descriptions', 
        'business_rules'
    ]
    
    source_conn = sqlite3.connect(source_db)
    target_conn = sqlite3.connect(target_db)
    
    for table in metadata_tables:
        print(f"迁移表: {table}")
        
        # 获取表结构
        source_cursor = source_conn.cursor()
        source_cursor.execute(f"SELECT sql FROM sqlite_master WHERE type='table' AND name='{table}'")
        create_sql = source_cursor.fetchone()[0]
        
        # 在目标数据库创建表
        target_cursor = target_conn.cursor()
        target_cursor.execute(f"DROP TABLE IF EXISTS {table}")
        target_cursor.execute(create_sql)
        
        # 迁移数据
        source_cursor.execute(f"SELECT * FROM {table}")
        rows = source_cursor.fetchall()
        
        if rows:
            column_count = len(rows[0])
            placeholders = ','.join(['?' for _ in range(column_count)])
            target_cursor.executemany(f"INSERT INTO {table} VALUES ({placeholders})", rows)
            print(f"  ✅ 迁移了 {len(rows)} 条记录")
    
    # 迁移视图
    views_to_migrate = ['financial_data_with_metadata', 'financial_data_columns_metadata']
    for view in views_to_migrate:
        source_cursor.execute(f"SELECT sql FROM sqlite_master WHERE type='view' AND name='{view}'")
        view_sql = source_cursor.fetchone()
        if view_sql:
            # 修改视图SQL以引用正确的数据库
            modified_sql = view_sql[0].replace('financial_data', 'business_db.financial_data')
            target_cursor.execute(f"DROP VIEW IF EXISTS {view}")
            target_cursor.execute(modified_sql)
            print(f"  ✅ 迁移视图: {view}")
    
    target_conn.commit()
    source_conn.close()
    target_conn.close()
    
    print("🎉 元数据迁移完成！")
```

### 步骤3: 更新Text2SQL服务

**修改元数据获取函数**:
```python
def get_financial_metadata(table_name: str = "financial_data") -> Dict[str, Any]:
    """从元数据库获取财务表的元数据信息"""
    
    # 连接元数据库
    metadata_db_path = settings.METADATA_DB_PATH
    
    try:
        conn = sqlite3.connect(metadata_db_path)
        cursor = conn.cursor()
        
        # 查询元数据（从resource.db）
        metadata = {
            "table_description": None,
            "column_descriptions": [],
            "business_rules": [],
            "has_metadata": False
        }
        
        # 查询表描述
        cursor.execute("""
            SELECT description, business_purpose, data_scale 
            FROM table_descriptions 
            WHERE table_name = ?
        """, (table_name,))
        
        # ... 其余查询逻辑
        
        conn.close()
        return metadata
        
    except Exception as e:
        print(f"获取元数据失败: {e}")
        return {"has_metadata": False}
```

**修改业务数据查询**:
```python
def execute_business_query(sql: str):
    """执行业务数据查询"""
    
    # 连接业务数据库
    business_db_path = settings.BUSINESS_DB_PATH
    
    try:
        conn = sqlite3.connect(business_db_path)
        cursor = conn.cursor()
        cursor.execute(sql)
        results = cursor.fetchall()
        columns = [description[0] for description in cursor.description]
        conn.close()
        
        return columns, results
        
    except Exception as e:
        print(f"业务查询失败: {e}")
        return None, None
```

## 📊 配置文件调整

### .env文件更新
```env
# 元数据库配置
METADATA_DB_PATH=C:\Users\<USER>\PycharmProjects\智能数据分析系统\resource.db

# 业务数据库配置  
BUSINESS_DB_PATH=C:\Users\<USER>\PycharmProjects\智能数据分析系统\fin_data.db

# 主数据库配置（用于系统表）
SQLITE_DB_PATH=C:\Users\<USER>\PycharmProjects\智能数据分析系统\resource.db
```

## 🎯 架构优势

### 1. 职责分离
- **resource.db**: 系统配置 + 元数据
- **fin_data.db**: 业务数据

### 2. 性能优化
- 元数据查询快速（小数据库）
- 业务查询不受元数据影响

### 3. 维护简单
- 元数据变更不影响业务数据
- 业务数据备份不包含系统配置

### 4. 扩展性强
- 可以轻松添加其他业务数据库
- 元数据系统可以管理多个业务库

## ✅ 验证步骤

### 1. 验证元数据迁移
```python
# 检查resource.db中的元数据
conn = sqlite3.connect("resource.db")
cursor = conn.cursor()
cursor.execute("SELECT COUNT(*) FROM table_descriptions")
print(f"表描述数量: {cursor.fetchone()[0]}")
```

### 2. 验证业务数据访问
```python
# 检查fin_data.db中的业务数据
conn = sqlite3.connect("fin_data.db")  
cursor = conn.cursor()
cursor.execute("SELECT COUNT(*) FROM financial_data")
print(f"业务记录数量: {cursor.fetchone()[0]}")
```

### 3. 验证跨库查询
```python
# 测试元数据增强的查询
metadata = get_financial_metadata()
print(f"元数据可用: {metadata['has_metadata']}")

# 测试业务数据查询
columns, results = execute_business_query("SELECT COUNT(*) FROM financial_data")
print(f"查询结果: {results}")
```

## 🎉 总结

这个方案完美平衡了：
- ✅ **架构清晰**: 元数据与业务数据分离
- ✅ **IDE兼容**: resource.db包含系统配置
- ✅ **性能优化**: 各司其职，互不影响
- ✅ **维护简单**: 职责明确，便于管理

**这是最符合软件架构最佳实践的解决方案！**
