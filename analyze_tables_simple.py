#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import os

def analyze_database_tables():
    """分析数据库中所有表的结构和作用"""
    
    db_path = 'fin_data.db'
    if not os.path.exists(db_path):
        print(f'数据库文件不存在: {db_path}')
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print("=" * 80)
        print("智能数据分析系统 - 数据库表结构分析")
        print("=" * 80)
        
        # 获取所有表名
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' ORDER BY name")
        tables = cursor.fetchall()
        
        print(f"\n📊 数据库包含 {len(tables)} 个表:")
        
        # 定义表的分类和作用
        table_info = {
            'financial_data': {
                'category': '💼 核心业务数据表',
                'purpose': '存储财务辅助科目余额数据，包含31个字段的完整财务信息，是系统的核心数据源'
            },
            'table_descriptions': {
                'category': '🗃️ 元数据管理表',
                'purpose': '存储数据库表的描述信息，为AI提供表级别的语义理解'
            },
            'column_descriptions': {
                'category': '🗃️ 元数据管理表', 
                'purpose': '存储每个字段的详细描述和AI理解要点，支持智能SQL生成'
            },
            'business_rules': {
                'category': '🗃️ 元数据管理表',
                'purpose': '存储财务业务规则，确保AI生成的SQL符合财务逻辑'
            },
            'dbconnection': {
                'category': '⚙️ 系统管理表',
                'purpose': '管理多数据库连接配置，支持SQLite/MySQL/PostgreSQL'
            },
            'schematable': {
                'category': '⚙️ 系统管理表',
                'purpose': '存储数据库表的结构信息，支持动态Schema发现'
            },
            'schemacolumn': {
                'category': '⚙️ 系统管理表',
                'purpose': '存储表字段的详细信息，包括数据类型和约束'
            },
            'schemarelationship': {
                'category': '⚙️ 系统管理表',
                'purpose': '存储表间关系，支持复杂查询的关联分析'
            },
            'valuemapping': {
                'category': '⚙️ 系统管理表',
                'purpose': '存储自然语言术语与数据库值的映射关系'
            },
            'chatsession': {
                'category': '💬 会话管理表',
                'purpose': '存储用户聊天会话信息，支持多轮对话'
            },
            'chatmessage': {
                'category': '💬 会话管理表',
                'purpose': '存储聊天消息详情，包括查询、SQL、结果等'
            },
            'chathistorysnapshot': {
                'category': '💬 会话管理表',
                'purpose': '存储查询历史的快照数据，支持会话恢复'
            },
            'sqlite_sequence': {
                'category': '🔧 SQLite系统表',
                'purpose': '管理自增ID序列'
            }
        }
        
        # 按类别分组显示
        categories = {}
        for table_name in [t[0] for t in tables]:
            info = table_info.get(table_name, {
                'category': '❓ 未知类别',
                'purpose': '未知用途的表'
            })
            category = info['category']
            if category not in categories:
                categories[category] = []
            categories[category].append((table_name, info['purpose']))
        
        # 显示分类结果
        for category, table_list in categories.items():
            print(f"\n{category}:")
            for table_name, purpose in table_list:
                # 获取记录数量
                try:
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                    count = cursor.fetchone()[0]
                    count_str = f"{count:,} 条记录"
                except:
                    count_str = "无法获取记录数"
                
                print(f"  📋 {table_name}: {purpose}")
                print(f"      📊 {count_str}")
        
        print(f"\n" + "=" * 80)
        print("表关系和数据流分析")
        print("=" * 80)
        
        print("""
🔄 数据流关系图:

1. 💼 核心业务层
   └── financial_data (723,333条财务记录)
       ├── 时间维度: year, month
       ├── 组织维度: accounting_organization, accounting_unit_name  
       ├── 科目维度: account_code, account_name, account_direction
       └── 金额维度: debit_amount, credit_amount, balance

2. 🗃️ 元数据层 (为AI提供语义理解)
   ├── table_descriptions → 表级别描述
   ├── column_descriptions → 字段级别描述 (31个字段)
   └── business_rules → 业务规则 (5个关键规则)

3. ⚙️ 系统管理层 (支持多数据库和Schema管理)
   ├── dbconnection → 数据库连接配置
   ├── schematable → 表结构信息
   ├── schemacolumn → 字段结构信息
   ├── schemarelationship → 表关系信息
   └── valuemapping → 值映射关系

4. 💬 会话管理层 (支持智能对话)
   ├── chatsession → 会话管理
   ├── chatmessage → 消息记录
   └── chathistorysnapshot → 历史快照

🎯 核心价值链:
用户查询 → 会话管理 → 元数据理解 → Schema分析 → SQL生成 → 业务数据查询 → 结果返回
        """)
        
        conn.close()
        
    except Exception as e:
        print(f"分析数据库时出错: {e}")

if __name__ == "__main__":
    analyze_database_tables()
